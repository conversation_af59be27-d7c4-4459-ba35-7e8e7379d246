"""
Foot Deformity Dataset Analyzer and Preprocessor
Analyzes the dataset structure and prepares data for ML training
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import cv2
from PIL import Image
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class FootDeformityDatasetAnalyzer:
    def __init__(self, dataset_path):
        self.dataset_path = Path(dataset_path)
        self.dataset_info = {}
        self.class_mapping = {}
        
    def analyze_dataset_structure(self):
        """Analyze the complete dataset structure"""
        print("🔍 Analyzing Dataset Structure...")
        print("=" * 50)
        
        dataset_stats = {
            'total_images': 0,
            'categories': {},
            'file_types': defaultdict(int),
            'image_sizes': [],
            'class_distribution': {}
        }
        
        # Analyze each main category
        for category_dir in self.dataset_path.iterdir():
            if category_dir.is_dir():
                category_name = category_dir.name
                print(f"\n📁 Category: {category_name}")
                
                category_stats = {
                    'subcategories': {},
                    'total_files': 0
                }
                
                # Analyze subcategories
                for subcat_dir in category_dir.iterdir():
                    if subcat_dir.is_dir():
                        subcat_name = subcat_dir.name
                        
                        # Count files in subcategory
                        image_files = []
                        for ext in ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']:
                            image_files.extend(list(subcat_dir.glob(f'*{ext}')))
                            image_files.extend(list(subcat_dir.glob(f'**/*{ext}')))
                        
                        file_count = len(image_files)
                        category_stats['subcategories'][subcat_name] = file_count
                        category_stats['total_files'] += file_count
                        
                        print(f"  📂 {subcat_name}: {file_count} files")
                        
                        # Update file type statistics
                        for img_file in image_files[:10]:  # Sample first 10 for analysis
                            dataset_stats['file_types'][img_file.suffix.lower()] += 1
                            
                            # Analyze image properties
                            try:
                                with Image.open(img_file) as img:
                                    dataset_stats['image_sizes'].append(img.size)
                            except Exception as e:
                                print(f"    ⚠️  Error reading {img_file.name}: {e}")
                
                dataset_stats['categories'][category_name] = category_stats
                dataset_stats['total_images'] += category_stats['total_files']
        
        self.dataset_info = dataset_stats
        return dataset_stats
    
    def create_class_mapping(self):
        """Create class mapping for multi-class classification"""
        print("\n🏷️  Creating Class Mapping...")
        
        # Define class mapping based on medical conditions
        class_mapping = {
            'normal': 0,
            'flatfoot': 1, 
            'foot_ulcer': 2,
            'hallux_valgus': 3
        }
        
        # Create detailed mapping for each subcategory
        detailed_mapping = {
            # Flatfoot categories
            'Normal': 'normal',
            'Abnormal Flatfoot': 'flatfoot',
            'notpesplanus (Normal)': 'normal',
            'pesplanus (Flat Foot)': 'flatfoot',
            
            # Foot ulcer categories  
            'Normal(Healthy skin)': 'normal',
            'Abnormal(Ulcer)': 'foot_ulcer',
            
            # Hallux Valgus categories
            'Hallux valgus': 'hallux_valgus',
            # Note: There's also a 'Normal' in Hallux Valgus
        }
        
        self.class_mapping = {
            'class_to_id': class_mapping,
            'id_to_class': {v: k for k, v in class_mapping.items()},
            'detailed_mapping': detailed_mapping
        }
        
        print("Class Mapping:")
        for class_name, class_id in class_mapping.items():
            print(f"  {class_id}: {class_name}")
            
        return self.class_mapping
    
    def generate_dataset_report(self):
        """Generate comprehensive dataset analysis report"""
        if not self.dataset_info:
            self.analyze_dataset_structure()
            
        print("\n📊 Dataset Analysis Report")
        print("=" * 50)
        
        # Overall statistics
        print(f"Total Images: {self.dataset_info['total_images']:,}")
        print(f"Categories: {len(self.dataset_info['categories'])}")
        
        # File type distribution
        print(f"\nFile Types:")
        for ext, count in self.dataset_info['file_types'].items():
            print(f"  {ext}: {count}")
        
        # Image size analysis
        if self.dataset_info['image_sizes']:
            sizes = self.dataset_info['image_sizes']
            widths = [s[0] for s in sizes]
            heights = [s[1] for s in sizes]
            
            print(f"\nImage Dimensions (sample):")
            print(f"  Width - Min: {min(widths)}, Max: {max(widths)}, Avg: {np.mean(widths):.1f}")
            print(f"  Height - Min: {min(heights)}, Max: {max(heights)}, Avg: {np.mean(heights):.1f}")
        
        # Category breakdown
        print(f"\nCategory Breakdown:")
        for category, stats in self.dataset_info['categories'].items():
            print(f"\n  📁 {category} ({stats['total_files']} files):")
            for subcat, count in stats['subcategories'].items():
                percentage = (count / stats['total_files']) * 100
                print(f"    • {subcat}: {count} ({percentage:.1f}%)")
    
    def create_unified_dataset_structure(self, output_dir="processed_dataset"):
        """Create a unified dataset structure for ML training"""
        print(f"\n🔄 Creating Unified Dataset Structure in '{output_dir}'...")
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Create class directories
        for class_name in ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']:
            (output_path / class_name).mkdir(exist_ok=True)
        
        dataset_manifest = []
        
        # Process each category
        for category_dir in self.dataset_path.iterdir():
            if not category_dir.is_dir():
                continue
                
            category_name = category_dir.name
            print(f"Processing {category_name}...")
            
            for subcat_dir in category_dir.iterdir():
                if not subcat_dir.is_dir():
                    continue
                    
                subcat_name = subcat_dir.name
                
                # Skip annotated datasets for now (they have different structure)
                if 'Annotated' in subcat_name:
                    continue
                
                # Determine target class
                target_class = self._map_subcategory_to_class(category_name, subcat_name)
                if not target_class:
                    print(f"  ⚠️  Skipping {subcat_name} - no class mapping")
                    continue
                
                # Copy images to unified structure
                image_files = []
                for ext in ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']:
                    image_files.extend(list(subcat_dir.glob(f'*{ext}')))
                    image_files.extend(list(subcat_dir.glob(f'**/*{ext}')))
                
                for i, img_file in enumerate(image_files):
                    # Create unique filename
                    new_filename = f"{category_name}_{subcat_name}_{i:04d}{img_file.suffix}"
                    new_path = output_path / target_class / new_filename
                    
                    # Copy file (or create symlink for efficiency)
                    try:
                        import shutil
                        shutil.copy2(img_file, new_path)
                        
                        # Add to manifest
                        dataset_manifest.append({
                            'filename': new_filename,
                            'class': target_class,
                            'class_id': self.class_mapping['class_to_id'][target_class],
                            'original_path': str(img_file),
                            'category': category_name,
                            'subcategory': subcat_name
                        })
                        
                    except Exception as e:
                        print(f"    ⚠️  Error copying {img_file.name}: {e}")
                
                print(f"  ✅ {subcat_name}: {len(image_files)} images → {target_class}")
        
        # Save dataset manifest
        manifest_df = pd.DataFrame(dataset_manifest)
        manifest_df.to_csv(output_path / 'dataset_manifest.csv', index=False)
        
        print(f"\n✅ Unified dataset created with {len(dataset_manifest)} images")
        print(f"📄 Dataset manifest saved to {output_path / 'dataset_manifest.csv'}")
        
        return output_path, manifest_df
    
    def _map_subcategory_to_class(self, category, subcategory):
        """Map subcategory to unified class"""
        mapping = {
            'Flatfoot': {
                'Normal': 'normal',
                'Abnormal Flatfoot': 'flatfoot'
            },
            'Foot ulcer': {
                'Normal(Healthy skin)': 'normal', 
                'Abnormal(Ulcer)': 'foot_ulcer'
            },
            'Hallux Valgus': {
                'Normal': 'normal',
                'Hallux valgus': 'hallux_valgus'
            }
        }
        
        return mapping.get(category, {}).get(subcategory)

if __name__ == "__main__":
    # Initialize analyzer
    analyzer = FootDeformityDatasetAnalyzer("Dataset")
    
    # Run complete analysis
    analyzer.analyze_dataset_structure()
    analyzer.create_class_mapping()
    analyzer.generate_dataset_report()
    
    # Create unified dataset
    output_dir, manifest = analyzer.create_unified_dataset_structure()
    
    print(f"\n🎯 Next Steps:")
    print(f"1. Review the unified dataset in '{output_dir}'")
    print(f"2. Check class distribution in dataset_manifest.csv")
    print(f"3. Proceed with data preprocessing and model training")
