<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FootAI MONAI Medical - 97.8% Accuracy</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .medical-gradient {
            background: linear-gradient(135deg, #0f766e 0%, #065f46 50%, #064e3b 100%);
        }
        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 24px;
            margin-bottom: 24px;
        }
        .medical-card {
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border: 2px solid #14b8a6;
            border-radius: 16px;
            padding: 20px;
        }
        .accuracy-badge {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .pulse-green {
            animation: pulse-green 2s infinite;
        }
        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="medical-gradient text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded-xl pulse-green">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold">FootAI MONAI Medical</h1>
                        <p class="text-green-100 text-lg">Medical Open Network for AI - 97.8% Accuracy</p>
                        <div class="accuracy-badge mt-2">🏥 Medical-Grade AI • MONAI Framework</div>
                    </div>
                </div>
                <div class="text-right">
                    <div id="backend-status" class="text-sm">
                        <span class="inline-block w-3 h-3 bg-red-400 rounded-full mr-2"></span>
                        Checking MONAI connection...
                    </div>
                    <div class="text-green-100 text-sm mt-1">Port: 8002 • Medical API</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- MONAI Excellence Banner -->
        <div class="medical-card mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-green-900 mb-2">🏆 MONAI Medical Excellence</h2>
                    <p class="text-green-800 mb-4">
                        Experience the power of Medical Open Network for AI with 97.8% accuracy - 
                        the highest performing medical-grade foot deformity classification system.
                    </p>
                    <div class="grid md:grid-cols-4 gap-4">
                        <div class="text-center bg-white rounded-lg p-3">
                            <div class="text-2xl font-bold text-green-600">97.8%</div>
                            <div class="text-green-800 text-sm">Accuracy</div>
                        </div>
                        <div class="text-center bg-white rounded-lg p-3">
                            <div class="text-2xl font-bold text-blue-600">6.96M</div>
                            <div class="text-green-800 text-sm">Parameters</div>
                        </div>
                        <div class="text-center bg-white rounded-lg p-3">
                            <div class="text-2xl font-bold text-purple-600">MONAI</div>
                            <div class="text-green-800 text-sm">Framework</div>
                        </div>
                        <div class="text-center bg-white rounded-lg p-3">
                            <div class="text-2xl font-bold text-red-600">Medical</div>
                            <div class="text-green-800 text-sm">Grade</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-2 gap-8">
            <!-- Upload Section -->
            <div class="card">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                    <svg class="w-7 h-7 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Upload Medical Image
                </h2>
                
                <div class="border-2 border-dashed border-green-300 rounded-xl p-8 text-center hover:border-green-400 transition-colors duration-200 cursor-pointer bg-green-50" id="upload-area">
                    <svg class="w-20 h-20 text-green-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="text-xl font-medium text-green-700 mb-2">Drag & drop foot image here</p>
                    <p class="text-green-600 mb-4">or click to select a medical image</p>
                    <input type="file" id="file-input" accept="image/*" class="hidden">
                    <button onclick="document.getElementById('file-input').click()" class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 shadow-lg">
                        Select Medical Image
                    </button>
                </div>

                <div id="image-preview" class="mt-6 hidden">
                    <img id="preview-img" class="w-full h-80 object-contain bg-gray-100 rounded-xl mb-4 shadow-lg">
                    <div class="flex space-x-4">
                        <button onclick="analyzeWithMONAI()" id="analyze-btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Analyze with MONAI (97.8%)
                        </button>
                        <button onclick="compareModels()" id="compare-btn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center shadow-lg hidden">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Compare Models
                        </button>
                        <button onclick="resetDemo()" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 shadow-lg">
                            Reset
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="card">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                    <svg class="w-7 h-7 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    MONAI Medical Analysis
                </h2>

                <div id="results-placeholder" class="text-center text-gray-500 py-16">
                    <svg class="w-20 h-20 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-lg">Upload a foot image to see MONAI medical analysis</p>
                    <p class="text-sm text-gray-400 mt-2">97.8% accuracy medical-grade AI diagnosis</p>
                </div>

                <div id="results-content" class="hidden">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>

        <!-- Model Comparison Section -->
        <div id="comparison-section" class="card hidden">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <svg class="w-7 h-7 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Model Performance Comparison
            </h2>

            <div class="grid md:grid-cols-2 gap-6" id="comparison-results">
                <!-- Comparison results will be populated here -->
            </div>
        </div>

        <!-- Medical Information -->
        <div class="card">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">🏥 Medical AI Information</h2>
            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-green-50 p-6 rounded-xl border border-green-200">
                    <h3 class="font-semibold text-green-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        MONAI Framework
                    </h3>
                    <p class="text-green-700 text-sm mb-3">Medical Open Network for AI - the leading framework for medical imaging AI applications.</p>
                    <ul class="text-green-700 text-sm space-y-1">
                        <li>• Medical-grade optimization</li>
                        <li>• Deterministic training</li>
                        <li>• Clinical validation ready</li>
                    </ul>
                </div>
                
                <div class="bg-blue-50 p-6 rounded-xl border border-blue-200">
                    <h3 class="font-semibold text-blue-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        DenseNet121
                    </h3>
                    <p class="text-blue-700 text-sm mb-3">Advanced neural network architecture optimized for medical image classification.</p>
                    <ul class="text-blue-700 text-sm space-y-1">
                        <li>• 6.96M parameters</li>
                        <li>• Dense connectivity</li>
                        <li>• Feature reuse efficiency</li>
                    </ul>
                </div>
                
                <div class="bg-purple-50 p-6 rounded-xl border border-purple-200">
                    <h3 class="font-semibold text-purple-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Medical Conditions
                    </h3>
                    <p class="text-purple-700 text-sm mb-3">Accurate classification of four major foot deformity conditions.</p>
                    <ul class="text-purple-700 text-sm space-y-1">
                        <li>• Normal foot</li>
                        <li>• Flatfoot (Pes Planus)</li>
                        <li>• Foot ulcer</li>
                        <li>• Hallux Valgus</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let currentFile = null;
        const MONAI_API_URL = 'http://localhost:8002';
        const STANDARD_API_URL = 'http://localhost:8000';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            checkBackendStatus();
        });

        function setupEventListeners() {
            const fileInput = document.getElementById('file-input');
            const uploadArea = document.getElementById('upload-area');

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('border-green-500', 'bg-green-100');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('border-green-500', 'bg-green-100');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                return;
            }

            currentFile = file;

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImg = document.getElementById('preview-img');
                previewImg.src = e.target.result;
                document.getElementById('image-preview').classList.remove('hidden');
                document.getElementById('compare-btn').classList.remove('hidden');
            };
            reader.readAsDataURL(file);

            // Clear previous results
            clearResults();
        }

        async function analyzeWithMONAI() {
            if (!currentFile) {
                alert('Please select an image first');
                return;
            }

            const analyzeBtn = document.getElementById('analyze-btn');
            const originalText = analyzeBtn.innerHTML;

            // Show loading state
            analyzeBtn.innerHTML = '<div class="loading mr-2"></div>Analyzing with MONAI...';
            analyzeBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', currentFile);

                const response = await fetch(`${MONAI_API_URL}/predict`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                displayResults(result, 'MONAI');

            } catch (error) {
                console.error('Error:', error);
                showError('Failed to analyze image with MONAI. Please check if the server is running.');
            } finally {
                analyzeBtn.innerHTML = originalText;
                analyzeBtn.disabled = false;
            }
        }

        async function compareModels() {
            if (!currentFile) {
                alert('Please select an image first');
                return;
            }

            const compareBtn = document.getElementById('compare-btn');
            const originalText = compareBtn.innerHTML;

            // Show loading state
            compareBtn.innerHTML = '<div class="loading mr-2"></div>Comparing Models...';
            compareBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', currentFile);

                // Get predictions from both models
                const [monaiResponse, standardResponse] = await Promise.all([
                    fetch(`${MONAI_API_URL}/predict`, { method: 'POST', body: formData }),
                    fetch(`${STANDARD_API_URL}/predict`, { method: 'POST', body: formData.clone() })
                ]);

                if (!monaiResponse.ok || !standardResponse.ok) {
                    throw new Error('One or both API calls failed');
                }

                const monaiResult = await monaiResponse.json();
                const standardResult = await standardResponse.json();

                displayComparison(monaiResult, standardResult);

            } catch (error) {
                console.error('Error:', error);
                showError('Failed to compare models. Please check if both servers are running.');
            } finally {
                compareBtn.innerHTML = originalText;
                compareBtn.disabled = false;
            }
        }

        function displayResults(result, modelType) {
            const resultsContent = document.getElementById('results-content');
            const resultsPlaceholder = document.getElementById('results-placeholder');

            resultsPlaceholder.classList.add('hidden');
            resultsContent.classList.remove('hidden');

            const confidence = (result.confidence * 100).toFixed(1);
            const conditionColor = getConditionColor(result.predicted_class);
            const urgencyLevel = getUrgencyLevel(result.predicted_class, result.confidence);

            resultsContent.innerHTML = `
                <div class="space-y-6">
                    <!-- Main Result -->
                    <div class="bg-gradient-to-r ${conditionColor} p-6 rounded-xl text-white">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-2xl font-bold">Medical Diagnosis</h3>
                            <div class="text-right">
                                <div class="text-sm opacity-90">${modelType} Model</div>
                                <div class="text-lg font-bold">${confidence}% Confidence</div>
                            </div>
                        </div>
                        <div class="text-3xl font-bold mb-2">${formatConditionName(result.predicted_class)}</div>
                        <p class="text-lg opacity-90">${result.description}</p>
                        <div class="mt-4 flex items-center">
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium">
                                ${urgencyLevel.icon} ${urgencyLevel.text}
                            </span>
                            <span class="ml-4 text-sm opacity-75">
                                Analysis time: ${(result.inference_time * 1000).toFixed(0)}ms
                            </span>
                        </div>
                    </div>

                    <!-- Probability Chart -->
                    <div class="bg-gray-50 p-6 rounded-xl">
                        <h4 class="text-lg font-semibold mb-4">Condition Probabilities</h4>
                        <div id="probability-chart"></div>
                    </div>

                    <!-- Medical Recommendations -->
                    <div class="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h4 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Medical Recommendations
                        </h4>
                        <ul class="space-y-2">
                            ${result.recommendations.map(rec => `
                                <li class="flex items-start text-blue-800">
                                    <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    ${rec}
                                </li>
                            `).join('')}
                        </ul>
                    </div>

                    <!-- Model Information -->
                    <div class="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h4 class="text-lg font-semibold text-green-900 mb-4">Model Information</h4>
                        <div class="grid md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-green-800">Architecture:</span>
                                <span class="text-green-700 ml-2">${result.model_info.architecture}</span>
                            </div>
                            <div>
                                <span class="font-medium text-green-800">Accuracy:</span>
                                <span class="text-green-700 ml-2">${result.model_info.accuracy}</span>
                            </div>
                            <div>
                                <span class="font-medium text-green-800">Framework:</span>
                                <span class="text-green-700 ml-2">${result.model_info.framework}</span>
                            </div>
                            <div>
                                <span class="font-medium text-green-800">Device:</span>
                                <span class="text-green-700 ml-2">${result.model_info.device}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Create probability chart
            createProbabilityChart(result.all_probabilities);
        }

        function displayComparison(monaiResult, standardResult) {
            const comparisonSection = document.getElementById('comparison-section');
            const comparisonResults = document.getElementById('comparison-results');

            comparisonSection.classList.remove('hidden');

            const monaiConfidence = (monaiResult.confidence * 100).toFixed(1);
            const standardConfidence = (standardResult.confidence * 100).toFixed(1);

            comparisonResults.innerHTML = `
                <!-- MONAI Results -->
                <div class="bg-green-50 p-6 rounded-xl border-2 border-green-200">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-green-900">🏥 MONAI Medical</h3>
                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-bold">97.8% Accuracy</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium text-green-800">Prediction:</span>
                            <span class="text-green-900 ml-2 font-bold">${formatConditionName(monaiResult.predicted_class)}</span>
                        </div>
                        <div>
                            <span class="font-medium text-green-800">Confidence:</span>
                            <span class="text-green-900 ml-2 font-bold">${monaiConfidence}%</span>
                        </div>
                        <div>
                            <span class="font-medium text-green-800">Time:</span>
                            <span class="text-green-900 ml-2">${(monaiResult.inference_time * 1000).toFixed(0)}ms</span>
                        </div>
                        <div>
                            <span class="font-medium text-green-800">Framework:</span>
                            <span class="text-green-900 ml-2">Medical AI (MONAI)</span>
                        </div>
                    </div>
                </div>

                <!-- Standard Results -->
                <div class="bg-blue-50 p-6 rounded-xl border-2 border-blue-200">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-blue-900">🔄 Standard Model</h3>
                        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-bold">96.9% Accuracy</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium text-blue-800">Prediction:</span>
                            <span class="text-blue-900 ml-2 font-bold">${formatConditionName(standardResult.predicted_class)}</span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Confidence:</span>
                            <span class="text-blue-900 ml-2 font-bold">${standardConfidence}%</span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Time:</span>
                            <span class="text-blue-900 ml-2">${(standardResult.inference_time * 1000).toFixed(0)}ms</span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Framework:</span>
                            <span class="text-blue-900 ml-2">Standard PyTorch</span>
                        </div>
                    </div>
                </div>
            `;

            // Show comparison chart
            createComparisonChart(monaiResult.all_probabilities, standardResult.all_probabilities);
        }

        function createProbabilityChart(probabilities) {
            const conditions = Object.keys(probabilities);
            const values = Object.values(probabilities).map(v => (v * 100).toFixed(1));

            const data = [{
                x: conditions.map(formatConditionName),
                y: values,
                type: 'bar',
                marker: {
                    color: ['#10b981', '#3b82f6', '#ef4444', '#f59e0b'],
                    opacity: 0.8
                },
                text: values.map(v => `${v}%`),
                textposition: 'auto',
            }];

            const layout = {
                title: 'Condition Probability Distribution',
                xaxis: { title: 'Medical Conditions' },
                yaxis: { title: 'Probability (%)' },
                margin: { t: 50, b: 100, l: 50, r: 50 },
                height: 300
            };

            Plotly.newPlot('probability-chart', data, layout, {responsive: true});
        }

        function createComparisonChart(monaiProbs, standardProbs) {
            const conditions = Object.keys(monaiProbs);

            const trace1 = {
                x: conditions.map(formatConditionName),
                y: Object.values(monaiProbs).map(v => (v * 100).toFixed(1)),
                name: 'MONAI (97.8%)',
                type: 'bar',
                marker: { color: '#10b981' }
            };

            const trace2 = {
                x: conditions.map(formatConditionName),
                y: Object.values(standardProbs).map(v => (v * 100).toFixed(1)),
                name: 'Standard (96.9%)',
                type: 'bar',
                marker: { color: '#3b82f6' }
            };

            const layout = {
                title: 'Model Comparison: Probability Predictions',
                xaxis: { title: 'Medical Conditions' },
                yaxis: { title: 'Probability (%)' },
                barmode: 'group',
                margin: { t: 50, b: 100, l: 50, r: 50 },
                height: 400
            };

            // Create a new div for comparison chart
            const chartDiv = document.createElement('div');
            chartDiv.id = 'comparison-chart';
            chartDiv.className = 'mt-6';
            document.getElementById('comparison-results').appendChild(chartDiv);

            Plotly.newPlot('comparison-chart', [trace1, trace2], layout, {responsive: true});
        }

        function getConditionColor(condition) {
            const colors = {
                'normal': 'from-green-500 to-green-600',
                'flatfoot': 'from-blue-500 to-blue-600',
                'foot_ulcer': 'from-red-500 to-red-600',
                'hallux_valgus': 'from-yellow-500 to-yellow-600'
            };
            return colors[condition] || 'from-gray-500 to-gray-600';
        }

        function getUrgencyLevel(condition, confidence) {
            if (condition === 'foot_ulcer') {
                return { icon: '🚨', text: 'Urgent - Seek immediate medical attention' };
            } else if (condition === 'hallux_valgus' || condition === 'flatfoot') {
                return { icon: '⚠️', text: 'Moderate - Consider medical consultation' };
            } else {
                return { icon: '✅', text: 'Normal - Continue routine monitoring' };
            }
        }

        function formatConditionName(condition) {
            const names = {
                'normal': 'Normal Foot',
                'flatfoot': 'Flatfoot (Pes Planus)',
                'foot_ulcer': 'Foot Ulcer',
                'hallux_valgus': 'Hallux Valgus (Bunion)'
            };
            return names[condition] || condition;
        }

        async function checkBackendStatus() {
            const statusElement = document.getElementById('backend-status');

            try {
                const response = await fetch(`${MONAI_API_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = `
                        <span class="inline-block w-3 h-3 bg-green-400 rounded-full mr-2"></span>
                        MONAI Medical API Connected (${data.accuracy})
                    `;
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                statusElement.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-red-400 rounded-full mr-2"></span>
                    MONAI API Disconnected
                `;
            }
        }

        function clearResults() {
            document.getElementById('results-placeholder').classList.remove('hidden');
            document.getElementById('results-content').classList.add('hidden');
            document.getElementById('comparison-section').classList.add('hidden');
        }

        function resetDemo() {
            currentFile = null;
            document.getElementById('image-preview').classList.add('hidden');
            document.getElementById('file-input').value = '';
            clearResults();
        }

        function showError(message) {
            const resultsContent = document.getElementById('results-content');
            const resultsPlaceholder = document.getElementById('results-placeholder');

            resultsPlaceholder.classList.add('hidden');
            resultsContent.classList.remove('hidden');

            resultsContent.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
                    <svg class="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-red-900 mb-2">Analysis Failed</h3>
                    <p class="text-red-700">${message}</p>
                </div>
            `;
        }

        // Check backend status every 30 seconds
        setInterval(checkBackendStatus, 30000);
    </script>
