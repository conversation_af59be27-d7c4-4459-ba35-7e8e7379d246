"""
Comprehensive Evaluation and Metrics for Foot Deformity Classification
Provides detailed model performance analysis and visualization
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    classification_report, confusion_matrix, accuracy_score,
    precision_recall_fscore_support, roc_auc_score, roc_curve,
    precision_recall_curve, average_precision_score
)
from sklearn.preprocessing import label_binarize
import itertools
from pathlib import Path
import json

class ModelEvaluator:
    """Comprehensive model evaluation and analysis"""
    
    def __init__(self, model, device, class_names=None):
        self.model = model
        self.device = device
        self.class_names = class_names or ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.num_classes = len(self.class_names)
        
        # Results storage
        self.predictions = []
        self.true_labels = []
        self.prediction_probs = []
        
    def evaluate_model(self, test_loader, verbose=True):
        """Evaluate model on test dataset"""
        
        print("🔍 Evaluating Model Performance...")
        print("=" * 40)
        
        self.model.eval()
        self.predictions = []
        self.true_labels = []
        self.prediction_probs = []
        
        total_samples = 0
        correct_predictions = 0
        
        with torch.no_grad():
            for batch_idx, (images, labels) in enumerate(test_loader):
                images, labels = images.to(self.device), labels.to(self.device)
                
                # Forward pass
                outputs = self.model(images)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get predictions
                _, predicted = torch.max(outputs, 1)
                
                # Store results
                self.predictions.extend(predicted.cpu().numpy())
                self.true_labels.extend(labels.cpu().numpy())
                self.prediction_probs.extend(probabilities.cpu().numpy())
                
                # Calculate accuracy
                total_samples += labels.size(0)
                correct_predictions += (predicted == labels).sum().item()
                
                if verbose and batch_idx % 20 == 0:
                    print(f"  Processed {batch_idx * len(images)}/{len(test_loader.dataset)} samples")
        
        # Convert to numpy arrays
        self.predictions = np.array(self.predictions)
        self.true_labels = np.array(self.true_labels)
        self.prediction_probs = np.array(self.prediction_probs)
        
        # Calculate overall accuracy
        overall_accuracy = 100. * correct_predictions / total_samples
        
        print(f"\n✅ Evaluation completed!")
        print(f"📊 Overall Accuracy: {overall_accuracy:.2f}%")
        print(f"📈 Total samples evaluated: {total_samples}")
        
        return overall_accuracy
    
    def generate_classification_report(self, save_path=None):
        """Generate detailed classification report"""
        
        print("\n📋 Classification Report")
        print("=" * 50)
        
        # Generate classification report
        report = classification_report(
            self.true_labels, 
            self.predictions,
            target_names=self.class_names,
            digits=4,
            output_dict=True
        )
        
        # Print formatted report
        report_str = classification_report(
            self.true_labels, 
            self.predictions,
            target_names=self.class_names,
            digits=4
        )
        print(report_str)
        
        # Save report
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report_str)
            print(f"📄 Classification report saved to {save_path}")
        
        return report
    
    def plot_confusion_matrix(self, save_path='confusion_matrix.png', normalize=True):
        """Plot confusion matrix"""
        
        # Calculate confusion matrix
        cm = confusion_matrix(self.true_labels, self.predictions)
        
        if normalize:
            cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            title = 'Normalized Confusion Matrix'
            fmt = '.2f'
            cm_plot = cm_normalized
        else:
            title = 'Confusion Matrix'
            fmt = 'd'
            cm_plot = cm
        
        # Create plot
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            cm_plot,
            annot=True,
            fmt=fmt,
            cmap='Blues',
            xticklabels=self.class_names,
            yticklabels=self.class_names,
            cbar_kws={'label': 'Proportion' if normalize else 'Count'}
        )
        
        plt.title(title, fontsize=16, fontweight='bold')
        plt.xlabel('Predicted Label', fontsize=12)
        plt.ylabel('True Label', fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        
        # Add accuracy for each class
        for i in range(len(self.class_names)):
            accuracy = cm[i, i] / cm[i, :].sum() * 100
            plt.text(i + 0.5, i - 0.3, f'{accuracy:.1f}%', 
                    ha='center', va='center', fontweight='bold', color='red')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📊 Confusion matrix saved as '{save_path}'")
        
        return cm
    
    def plot_roc_curves(self, save_path='roc_curves.png'):
        """Plot ROC curves for each class"""
        
        # Binarize labels for multiclass ROC
        y_true_bin = label_binarize(self.true_labels, classes=range(self.num_classes))
        
        plt.figure(figsize=(12, 8))
        
        # Plot ROC curve for each class
        for i in range(self.num_classes):
            fpr, tpr, _ = roc_curve(y_true_bin[:, i], self.prediction_probs[:, i])
            auc_score = roc_auc_score(y_true_bin[:, i], self.prediction_probs[:, i])
            
            plt.plot(fpr, tpr, linewidth=2, 
                    label=f'{self.class_names[i]} (AUC = {auc_score:.3f})')
        
        # Plot diagonal line
        plt.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.7)
        
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate', fontsize=12)
        plt.ylabel('True Positive Rate', fontsize=12)
        plt.title('ROC Curves for Each Class', fontsize=16, fontweight='bold')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📈 ROC curves saved as '{save_path}'")
    
    def plot_precision_recall_curves(self, save_path='pr_curves.png'):
        """Plot Precision-Recall curves for each class"""
        
        # Binarize labels
        y_true_bin = label_binarize(self.true_labels, classes=range(self.num_classes))
        
        plt.figure(figsize=(12, 8))
        
        # Plot PR curve for each class
        for i in range(self.num_classes):
            precision, recall, _ = precision_recall_curve(y_true_bin[:, i], self.prediction_probs[:, i])
            ap_score = average_precision_score(y_true_bin[:, i], self.prediction_probs[:, i])
            
            plt.plot(recall, precision, linewidth=2,
                    label=f'{self.class_names[i]} (AP = {ap_score:.3f})')
        
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('Recall', fontsize=12)
        plt.ylabel('Precision', fontsize=12)
        plt.title('Precision-Recall Curves for Each Class', fontsize=16, fontweight='bold')
        plt.legend(loc="lower left")
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📈 Precision-Recall curves saved as '{save_path}'")
    
    def analyze_class_performance(self):
        """Detailed analysis of per-class performance"""
        
        print("\n🔍 Per-Class Performance Analysis")
        print("=" * 50)
        
        # Calculate metrics for each class
        precision, recall, f1, support = precision_recall_fscore_support(
            self.true_labels, self.predictions, average=None
        )
        
        # Create detailed analysis
        analysis_data = []
        for i, class_name in enumerate(self.class_names):
            class_data = {
                'Class': class_name,
                'Precision': precision[i],
                'Recall': recall[i],
                'F1-Score': f1[i],
                'Support': support[i],
                'Accuracy': np.sum((self.true_labels == i) & (self.predictions == i)) / np.sum(self.true_labels == i)
            }
            analysis_data.append(class_data)
        
        # Create DataFrame
        df_analysis = pd.DataFrame(analysis_data)
        
        # Display results
        print(df_analysis.round(4))
        
        # Identify best and worst performing classes
        best_class = df_analysis.loc[df_analysis['F1-Score'].idxmax(), 'Class']
        worst_class = df_analysis.loc[df_analysis['F1-Score'].idxmin(), 'Class']
        
        print(f"\n🏆 Best performing class: {best_class} (F1: {df_analysis['F1-Score'].max():.3f})")
        print(f"⚠️  Worst performing class: {worst_class} (F1: {df_analysis['F1-Score'].min():.3f})")
        
        return df_analysis
    
    def generate_prediction_confidence_analysis(self, save_path='confidence_analysis.png'):
        """Analyze prediction confidence distribution"""
        
        # Calculate prediction confidence (max probability)
        confidence_scores = np.max(self.prediction_probs, axis=1)
        correct_predictions = (self.predictions == self.true_labels)
        
        plt.figure(figsize=(15, 5))
        
        # Overall confidence distribution
        plt.subplot(1, 3, 1)
        plt.hist(confidence_scores, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(np.mean(confidence_scores), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(confidence_scores):.3f}')
        plt.xlabel('Prediction Confidence')
        plt.ylabel('Frequency')
        plt.title('Overall Confidence Distribution')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Confidence by correctness
        plt.subplot(1, 3, 2)
        correct_conf = confidence_scores[correct_predictions]
        incorrect_conf = confidence_scores[~correct_predictions]
        
        plt.hist(correct_conf, bins=20, alpha=0.7, label='Correct', color='green')
        plt.hist(incorrect_conf, bins=20, alpha=0.7, label='Incorrect', color='red')
        plt.xlabel('Prediction Confidence')
        plt.ylabel('Frequency')
        plt.title('Confidence by Prediction Correctness')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Confidence by class
        plt.subplot(1, 3, 3)
        for i, class_name in enumerate(self.class_names):
            class_mask = self.true_labels == i
            class_conf = confidence_scores[class_mask]
            plt.hist(class_conf, bins=15, alpha=0.6, label=class_name)
        
        plt.xlabel('Prediction Confidence')
        plt.ylabel('Frequency')
        plt.title('Confidence Distribution by Class')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📊 Confidence analysis saved as '{save_path}'")
        
        # Print confidence statistics
        print(f"\n📈 Confidence Statistics:")
        print(f"  Mean confidence: {np.mean(confidence_scores):.3f}")
        print(f"  Std confidence: {np.std(confidence_scores):.3f}")
        print(f"  Mean confidence (correct): {np.mean(correct_conf):.3f}")
        print(f"  Mean confidence (incorrect): {np.mean(incorrect_conf):.3f}")
    
    def save_evaluation_results(self, save_dir='evaluation_results'):
        """Save all evaluation results"""
        
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        # Save predictions and probabilities
        results_data = {
            'true_labels': self.true_labels.tolist(),
            'predictions': self.predictions.tolist(),
            'prediction_probs': self.prediction_probs.tolist(),
            'class_names': self.class_names
        }
        
        with open(save_path / 'evaluation_results.json', 'w') as f:
            json.dump(results_data, f, indent=2)
        
        # Generate all plots and reports
        self.generate_classification_report(save_path / 'classification_report.txt')
        self.plot_confusion_matrix(save_path / 'confusion_matrix.png')
        self.plot_roc_curves(save_path / 'roc_curves.png')
        self.plot_precision_recall_curves(save_path / 'pr_curves.png')
        self.generate_prediction_confidence_analysis(save_path / 'confidence_analysis.png')
        
        # Save class analysis
        class_analysis = self.analyze_class_performance()
        class_analysis.to_csv(save_path / 'class_performance.csv', index=False)
        
        print(f"\n💾 All evaluation results saved to '{save_dir}'")

def main():
    """Example usage of the evaluator"""
    
    print("🔍 Model Evaluation Example")
    print("=" * 40)
    
    # This would typically be called after training
    # evaluator = ModelEvaluator(trained_model, device)
    # evaluator.evaluate_model(test_loader)
    # evaluator.save_evaluation_results()
    
    print("📋 Evaluation pipeline ready for use!")

if __name__ == "__main__":
    main()
