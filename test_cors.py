"""
Test CORS functionality of the backend server
"""

import requests
import json

def test_cors():
    """Test CORS headers and functionality"""
    
    print("🧪 Testing CORS Configuration")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test health endpoint with CORS
    try:
        print("🔍 Testing /health endpoint...")
        response = requests.get(f"{base_url}/health")
        
        print(f"   Status: {response.status_code}")
        print(f"   CORS Headers:")
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        for header, value in cors_headers.items():
            print(f"     {header}: {value}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data}")
            print("   ✅ Health endpoint working")
        else:
            print("   ❌ Health endpoint failed")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test model info endpoint
    try:
        print("\n🔍 Testing /model/info endpoint...")
        response = requests.get(f"{base_url}/model/info")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Model: {data['architecture']}")
            print(f"   Classes: {len(data['class_names'])}")
            print(f"   Parameters: {data['total_parameters']:,}")
            print("   ✅ Model info endpoint working")
        else:
            print("   ❌ Model info endpoint failed")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test classes endpoint
    try:
        print("\n🔍 Testing /classes endpoint...")
        response = requests.get(f"{base_url}/classes")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Available classes:")
            for cls in data['classes']:
                print(f"     - {cls['name']}: {cls['description']}")
            print("   ✅ Classes endpoint working")
        else:
            print("   ❌ Classes endpoint failed")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 CORS Test Summary:")
    print(f"   Backend URL: {base_url}")
    print(f"   CORS Policy: Allow all origins (*)")
    print(f"   Status: ✅ Ready for frontend integration")

if __name__ == "__main__":
    test_cors()
