"""
Test 99.3% Hallux Valgus Detection Model on External Dataset
Validate the trained 99.3% accuracy model on your external images
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from PIL import Image
import torchvision.transforms as transforms
import json
import time

# MONAI imports
import monai
from monai.networks.nets import DenseNet121

class HalluxValgus99Tester:
    """Test the 99.3% Hallux Valgus detection model"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.model = None
        
        print("🎯 TESTING 99.3% HALLUX VALGUS DETECTION MODEL")
        print("=" * 70)
        print(f"Device: {self.device}")
        print(f"MONAI Version: {monai.__version__}")
    
    def load_99_percent_model(self):
        """Load the trained 99.3% model"""
        
        model_path = Path("models/hallux_valgus_95_percent.pth")
        if not model_path.exists():
            raise FileNotFoundError(f"99.3% model not found: {model_path}")
        
        print(f"📦 Loading 99.3% Hallux Valgus model...")
        
        # Create model architecture (same as training)
        self.model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=False
        )
        
        # Add dropout layer (same as training)
        original_classifier = self.model.class_layers.out
        self.model.class_layers.out = torch.nn.Sequential(
            torch.nn.Dropout(0.3),
            original_classifier
        )
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        # Get model info
        best_recall = checkpoint.get('best_hallux_valgus_recall', 0)
        val_acc = checkpoint.get('val_acc', 0)
        
        print(f"✅ 99.3% model loaded successfully!")
        print(f"📊 Hallux Valgus recall: {best_recall:.1f}%")
        print(f"📊 Validation accuracy: {val_acc:.1f}%")
        
        return True
    
    def robust_image_loader(self, image_path):
        """Ultra-robust image loading"""
        
        try:
            # Method 1: Standard RGB
            image = Image.open(image_path).convert('RGB')
            return image
        except Exception:
            try:
                # Method 2: RGBA to RGB
                img_rgba = Image.open(image_path).convert('RGBA')
                background = Image.new('RGB', img_rgba.size, (255, 255, 255))
                background.paste(img_rgba, mask=img_rgba.split()[-1])
                return background
            except Exception:
                try:
                    # Method 3: Grayscale to RGB
                    img_gray = Image.open(image_path).convert('L')
                    return Image.merge('RGB', (img_gray, img_gray, img_gray))
                except Exception:
                    return None
    
    def predict_with_99_percent_model(self, image_path):
        """Predict with 99.3% accuracy model"""
        
        # Load image
        image = self.robust_image_loader(image_path)
        if image is None:
            return None
        
        # Create transform (same as training)
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        try:
            # Apply transform
            image_tensor = transform(image).unsqueeze(0).to(self.device)
            
            # Make prediction
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = F.softmax(outputs, dim=1)
                predicted_class_id = torch.argmax(outputs, dim=1).item()
                confidence = probabilities[0][predicted_class_id].item()
            
            return {
                'image_path': str(image_path),
                'predicted_class_id': predicted_class_id,
                'predicted_class': self.class_names[predicted_class_id],
                'confidence': confidence,
                'probabilities': probabilities[0].cpu().numpy().tolist(),
                'hallux_valgus_probability': probabilities[0][3].item()  # Specific HV probability
            }
            
        except Exception as e:
            print(f"❌ Prediction failed for {image_path}: {e}")
            return None
    
    def test_external_dataset(self, dataset_path):
        """Test on external Hallux Valgus dataset"""
        
        dataset_path = Path(dataset_path)
        print(f"\n🧪 TESTING 99.3% MODEL ON EXTERNAL DATASET")
        print(f"Dataset: {dataset_path}")
        print("-" * 60)
        
        # Check if path exists
        if not dataset_path.exists():
            print(f"❌ Dataset path not found: {dataset_path}")
            print(f"🔧 Please check the path and try again")
            return None
        
        # Find all image files
        print(f"🔍 Searching for images...")
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        all_images = []
        
        # Search in main directory
        for ext in image_extensions:
            all_images.extend(dataset_path.glob(f"*{ext}"))
            all_images.extend(dataset_path.glob(f"*{ext.upper()}"))
        
        # Search in subdirectories
        for subdir in dataset_path.iterdir():
            if subdir.is_dir():
                for ext in image_extensions:
                    all_images.extend(subdir.glob(f"*{ext}"))
                    all_images.extend(subdir.glob(f"*{ext.upper()}"))
        
        if not all_images:
            print(f"❌ No images found in {dataset_path}")
            print(f"🔍 Searched for: {image_extensions}")
            print(f"📁 Check if images are in subdirectories")
            return None
        
        print(f"📊 Found {len(all_images)} images")
        
        # Show sample of found images
        print(f"\n📋 Sample images found:")
        for i, img_path in enumerate(all_images[:10]):
            print(f"   {i+1:2d}. {img_path.name}")
        if len(all_images) > 10:
            print(f"   ... and {len(all_images) - 10} more images")
        
        # Process all images
        print(f"\n🔄 Processing with 99.3% Hallux Valgus model...")
        results = []
        successful = 0
        failed = 0
        
        start_time = time.time()
        
        for i, image_path in enumerate(all_images):
            if i % 20 == 0 and i > 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(all_images) - i) * avg_time
                print(f"Progress: {i}/{len(all_images)} ({i/len(all_images)*100:.1f}%) - ETA: {remaining:.0f}s")
            
            # Make prediction
            prediction = self.predict_with_99_percent_model(image_path)
            
            if prediction is not None:
                results.append(prediction)
                successful += 1
                
                # Show first 20 predictions
                if i < 20:
                    pred_class = prediction['predicted_class']
                    confidence = prediction['confidence']
                    hv_prob = prediction['hallux_valgus_probability']
                    status = "🦶" if pred_class == 'hallux_valgus' else "❓"
                    print(f"   {status} {image_path.name}: {pred_class} ({confidence:.3f}) HV:{hv_prob:.3f}")
            else:
                failed += 1
                if i < 20:
                    print(f"   ❌ {image_path.name}: Failed to process")
        
        processing_time = time.time() - start_time
        
        print(f"\n📊 PROCESSING COMPLETED!")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        print(f"   ⏱️ Total time: {processing_time:.1f}s")
        print(f"   🚀 Average: {processing_time/len(all_images):.2f}s per image")
        
        if successful == 0:
            print("❌ No successful predictions made")
            return None
        
        # Analyze results with 99.3% model
        return self.analyze_99_percent_results(results, dataset_path)
    
    def analyze_99_percent_results(self, results, dataset_path):
        """Analyze results from 99.3% model"""
        
        print(f"\n📊 99.3% MODEL ANALYSIS RESULTS")
        print("=" * 60)
        
        total = len(results)
        
        # Overall statistics
        confidences = [r['confidence'] for r in results]
        hv_probabilities = [r['hallux_valgus_probability'] for r in results]
        avg_confidence = np.mean(confidences)
        avg_hv_prob = np.mean(hv_probabilities)
        
        print(f"Total predictions: {total}")
        print(f"Average confidence: {avg_confidence:.3f}")
        print(f"Average Hallux Valgus probability: {avg_hv_prob:.3f}")
        
        # Class distribution
        print(f"\nPredicted Class Distribution:")
        class_counts = {}
        for class_name in self.class_names:
            count = sum(1 for r in results if r['predicted_class'] == class_name)
            class_counts[class_name] = count
            percentage = count/total*100
            icon = "🦶" if class_name == 'hallux_valgus' else "📊"
            print(f"   {icon} {class_name:15}: {count:4d} ({percentage:5.1f}%)")
        
        # Hallux Valgus specific analysis
        hallux_valgus_predictions = class_counts.get('hallux_valgus', 0)
        hallux_valgus_percentage = hallux_valgus_predictions / total * 100
        
        print(f"\n🦶 HALLUX VALGUS DETECTION ANALYSIS:")
        print(f"   Expected class: Hallux Valgus")
        print(f"   Detected as Hallux Valgus: {hallux_valgus_predictions}/{total} ({hallux_valgus_percentage:.1f}%)")
        
        # Confidence analysis for Hallux Valgus
        hv_results = [r for r in results if r['predicted_class'] == 'hallux_valgus']
        if hv_results:
            hv_confidences = [r['confidence'] for r in hv_results]
            avg_hv_confidence = np.mean(hv_confidences)
            high_conf_hv = sum(1 for c in hv_confidences if c > 0.8)
            
            print(f"   Average HV confidence: {avg_hv_confidence:.3f}")
            print(f"   High confidence HV (>80%): {high_conf_hv}/{len(hv_results)} ({high_conf_hv/len(hv_results)*100:.1f}%)")
        
        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        if hallux_valgus_percentage >= 95:
            print(f"   🎉 EXCELLENT! 99.3% model achieving 95%+ detection rate!")
            status = "EXCELLENT"
        elif hallux_valgus_percentage >= 85:
            print(f"   ✅ VERY GOOD! Strong detection performance")
            status = "VERY GOOD"
        elif hallux_valgus_percentage >= 70:
            print(f"   👍 GOOD! Reasonable detection performance")
            status = "GOOD"
        else:
            print(f"   ⚠️ NEEDS REVIEW! Lower than expected detection rate")
            status = "NEEDS REVIEW"
        
        # Save results
        self.save_99_percent_results(results, dataset_path, hallux_valgus_percentage)
        
        return {
            'total_predictions': total,
            'hallux_valgus_detection_rate': hallux_valgus_percentage,
            'average_confidence': avg_confidence,
            'average_hv_probability': avg_hv_prob,
            'class_distribution': class_counts,
            'performance_status': status,
            'results': results
        }
    
    def save_99_percent_results(self, results, dataset_path, detection_rate):
        """Save 99.3% model results"""
        
        # Create results directory
        results_dir = Path("external_validation_results")
        results_dir.mkdir(exist_ok=True)
        
        # Save detailed results
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"hallux_valgus_99_percent_{timestamp}.json"
        
        validation_data = {
            'model_info': {
                'name': '99.3% Hallux Valgus Specialist',
                'architecture': 'MONAI DenseNet121',
                'training_recall': 99.3,
                'validation_accuracy': 95.0
            },
            'dataset_info': {
                'path': str(dataset_path),
                'total_images': len(results),
                'detection_rate': detection_rate
            },
            'timestamp': timestamp,
            'results': results
        }
        
        with open(results_file, 'w') as f:
            json.dump(validation_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Create summary report
        summary_file = results_dir / f"hallux_valgus_99_summary_{timestamp}.txt"
        with open(summary_file, 'w') as f:
            f.write("99.3% Hallux Valgus Model - External Validation Summary\n")
            f.write("=" * 60 + "\n")
            f.write(f"Dataset: {dataset_path}\n")
            f.write(f"Total images: {len(results)}\n")
            f.write(f"Hallux Valgus detection rate: {detection_rate:.1f}%\n")
            f.write(f"Model training recall: 99.3%\n")
            f.write(f"Timestamp: {timestamp}\n")
        
        print(f"📄 Summary saved to: {summary_file}")

def main():
    """Main testing function"""
    
    # Your external dataset path
    external_dataset_path = r"C:\Users\<USER>\Desktop\External Vaildation Dataset"
    
    try:
        # Initialize tester
        tester = HalluxValgus99Tester()
        
        # Load 99.3% model
        tester.load_99_percent_model()
        
        # Test on external dataset
        results = tester.test_external_dataset(external_dataset_path)
        
        if results:
            detection_rate = results['hallux_valgus_detection_rate']
            status = results['performance_status']
            
            print(f"\n🎉 99.3% MODEL EXTERNAL VALIDATION COMPLETED!")
            print(f"   🎯 Detection rate: {detection_rate:.1f}%")
            print(f"   📊 Performance: {status}")
            print(f"   💾 Results saved in 'external_validation_results' directory")
            
            if detection_rate >= 95:
                print(f"\n🏆 SUCCESS! 99.3% model maintains excellent performance on external data!")
            elif detection_rate >= 85:
                print(f"\n✅ GOOD! Strong performance on external dataset")
            else:
                print(f"\n📈 IMPROVEMENT OPPORTUNITY: Consider domain adaptation")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"1. Check if 99.3% model exists: models/hallux_valgus_95_percent.pth")
        print(f"2. Verify external dataset path: {external_dataset_path}")
        print(f"3. Ensure images are accessible")

if __name__ == "__main__":
    main()
