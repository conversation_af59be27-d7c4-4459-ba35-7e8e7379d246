"""
Test the enhanced API with real images
"""

import requests
from pathlib import Path
import json

def test_enhanced_api():
    """Test enhanced API predictions"""
    
    print("🧪 Testing Enhanced API")
    print("=" * 40)
    
    # Test with images from each class
    processed_dataset = Path("processed_dataset")
    test_results = []
    
    for class_name in ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']:
        class_dir = processed_dataset / class_name
        if class_dir.exists():
            image_files = list(class_dir.glob("*.jpg"))
            if image_files:
                test_image = image_files[0]
                
                print(f"\n📸 Testing {class_name} image: {test_image.name}")
                
                try:
                    with open(test_image, 'rb') as f:
                        files = {'file': f}
                        response = requests.post('http://localhost:8000/predict', files=files, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        predicted_class = result['predicted_class']
                        confidence = result['confidence']
                        correct = predicted_class == class_name
                        enhanced = result.get('enhancement_applied', False)
                        raw_prediction = result.get('raw_prediction', predicted_class)
                        
                        print(f"   True: {class_name}")
                        print(f"   Predicted: {predicted_class}")
                        print(f"   Confidence: {confidence:.1%}")
                        print(f"   Correct: {'✅' if correct else '❌'}")
                        print(f"   Enhanced: {'✅' if enhanced else '❌'}")
                        print(f"   Processing: {result['inference_time']*1000:.1f}ms")
                        
                        if raw_prediction != predicted_class:
                            print(f"   Enhancement: {raw_prediction} → {predicted_class}")
                        
                        # Show top probabilities
                        sorted_probs = sorted(result['all_probabilities'].items(), key=lambda x: x[1], reverse=True)
                        print(f"   Top predictions:")
                        for i, (cls, prob) in enumerate(sorted_probs[:2]):
                            print(f"     {i+1}. {cls}: {prob:.1%}")
                        
                        test_results.append({
                            'true_class': class_name,
                            'predicted_class': predicted_class,
                            'correct': correct,
                            'confidence': confidence,
                            'enhanced': enhanced,
                            'raw_prediction': raw_prediction
                        })
                        
                    else:
                        print(f"   ❌ API Error: {response.status_code}")
                        print(f"   Response: {response.text}")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
    
    # Summary
    if test_results:
        accuracy = sum(r['correct'] for r in test_results) / len(test_results) * 100
        enhanced_count = sum(r['enhanced'] for r in test_results)
        avg_confidence = sum(r['confidence'] for r in test_results) / len(test_results) * 100
        
        print(f"\n📊 ENHANCED API RESULTS:")
        print(f"   🎯 Accuracy: {accuracy:.1f}% ({sum(r['correct'] for r in test_results)}/{len(test_results)})")
        print(f"   🔧 Enhanced predictions: {enhanced_count}/{len(test_results)}")
        print(f"   📈 Average confidence: {avg_confidence:.1f}%")
        
        # Show improvements
        improvements = [r for r in test_results if r['enhanced'] and r['correct']]
        if improvements:
            print(f"   ✅ Successful enhancements: {len(improvements)}")
            for imp in improvements:
                print(f"      {imp['raw_prediction']} → {imp['predicted_class']} (correct!)")
        
        if accuracy >= 75:
            print(f"   ✅ Good accuracy achieved!")
            return True
        else:
            print(f"   ⚠️  Accuracy needs further improvement")
            return False
    
    return False

if __name__ == "__main__":
    success = test_enhanced_api()
    
    if success:
        print(f"\n🎉 ENHANCED API IS WORKING WELL!")
        print(f"✅ Your FootAI system now has improved accuracy!")
        print(f"🌐 Test it in the demo: http://localhost:3001/simple_demo.html")
    else:
        print(f"\n⚠️  API needs further improvements")
