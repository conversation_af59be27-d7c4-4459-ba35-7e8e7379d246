"""
Run 32-Epoch Comprehensive Model Training
Train all models (EfficientNet, MONAI, ResNet50, ViT, InceptionNet) for 32 epochs
"""

import subprocess
import sys
import time
from pathlib import Path

def install_requirements():
    """Install required packages"""
    
    print("📦 INSTALLING REQUIRED PACKAGES")
    print("-" * 50)
    
    packages = [
        "timm",  # For Vision Transformer
        "matplotlib",  # For plotting
        "seaborn"  # For visualization
    ]
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed successfully")
        except Exception as e:
            print(f"⚠️ Failed to install {package}: {e}")

def run_comprehensive_training():
    """Run the comprehensive training"""
    
    print("🚀 STARTING 32-EPOCH COMPREHENSIVE TRAINING")
    print("=" * 70)
    print("Models to train:")
    print("  1. 🔥 EfficientNet-B3 (MONAI)")
    print("  2. 🧠 MONAI DenseNet121")
    print("  3. 🏗️ ResNet50")
    print("  4. 👁️ Vision Transformer (ViT)")
    print("  5. 🌟 InceptionNet")
    print("=" * 70)
    
    # Check if dataset exists
    dataset_path = Path("processed_dataset/dataset_manifest.csv")
    if not dataset_path.exists():
        print("❌ Dataset not found. Please ensure processed_dataset exists.")
        return False
    
    # Create models directory
    Path("models").mkdir(exist_ok=True)
    
    # Run training
    try:
        import comprehensive_model_training
        trainer = comprehensive_model_training.ComprehensiveModelTrainer(epochs=32)
        trainer.run_comprehensive_training()
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution function"""
    
    start_time = time.time()
    
    print("🎯 32-EPOCH COMPREHENSIVE MODEL TRAINING")
    print("=" * 70)
    print("This will train and compare 5 different architectures:")
    print("• EfficientNet, MONAI DenseNet, ResNet50, ViT, InceptionNet")
    print("• Each model will be trained for 32 epochs")
    print("• Performance will be compared on Hallux Valgus detection")
    print("=" * 70)
    
    # Install requirements
    install_requirements()
    
    # Run training
    success = run_comprehensive_training()
    
    total_time = time.time() - start_time
    
    if success:
        print(f"\n🎉 32-EPOCH TRAINING COMPLETED SUCCESSFULLY!")
        print(f"⏱️ Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        print(f"📊 Check the comparison report for best model")
        print(f"💾 Models saved in 'models/' directory")
        print(f"📋 Results saved in JSON format")
    else:
        print(f"\n❌ Training failed after {total_time:.1f} seconds")
        print(f"🔧 Check error messages above for troubleshooting")

if __name__ == "__main__":
    main()
