"""
Test the Improved Model Performance
Quick evaluation of the improved model vs original
"""

import torch
import torch.nn.functional as F
import pandas as pd
import numpy as np
from pathlib import Path
import random

# Import our modules
from model_architectures import create_model
from inference_pipeline import FootDeformityPredictor

def compare_models():
    """Compare original vs improved model performance"""
    
    print("🔍 COMPARING MODEL PERFORMANCE")
    print("=" * 50)
    
    models_dir = Path("models")
    
    # Check available models
    original_model = models_dir / "best_resnet50_model.pth"
    improved_model = models_dir / "improved_resnet50_model.pth"
    
    models_to_test = []
    if original_model.exists():
        models_to_test.append(("Original Model", original_model))
    if improved_model.exists():
        models_to_test.append(("Improved Model", improved_model))
    
    if not models_to_test:
        print("❌ No models found for comparison!")
        return
    
    # Load dataset for testing
    manifest_path = Path("processed_dataset/dataset_manifest.csv")
    if not manifest_path.exists():
        print("❌ Dataset manifest not found!")
        return
    
    df = pd.read_csv(manifest_path)
    class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
    
    # Get sample images from each class for testing
    test_samples = []
    for class_name in class_names:
        class_df = df[df['class'] == class_name]
        if len(class_df) > 0:
            # Get 5 random samples from each class
            samples = class_df.sample(min(5, len(class_df)))
            for _, row in samples.iterrows():
                test_samples.append({
                    'filename': row['filename'],
                    'true_class': class_name,
                    'class_id': row['class_id'],
                    'path': Path("processed_dataset") / class_name / row['filename']
                })
    
    print(f"📊 Testing with {len(test_samples)} sample images")
    
    # Test each model
    results = {}
    
    for model_name, model_path in models_to_test:
        print(f"\n🧠 Testing {model_name}...")
        
        try:
            # Load model
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = create_model('resnet50', num_classes=4, pretrained=False)
            
            if 'improved' in model_path.name or 'best' in model_path.name:
                checkpoint = torch.load(model_path, map_location=device)
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    if 'best_val_acc' in checkpoint:
                        print(f"   📊 Validation Accuracy: {checkpoint['best_val_acc']:.1f}%")
                else:
                    model.load_state_dict(checkpoint)
            else:
                model.load_state_dict(torch.load(model_path, map_location=device))
            
            model.to(device)
            model.eval()
            
            # Test predictions
            correct_predictions = 0
            class_correct = {class_name: 0 for class_name in class_names}
            class_total = {class_name: 0 for class_name in class_names}
            
            print(f"   🔍 Testing predictions...")
            
            for sample in test_samples:
                true_class = sample['true_class']
                class_total[true_class] += 1
                
                # For this test, we'll use dummy input since we need proper image loading
                # In a real scenario, you'd load and preprocess the actual image
                dummy_input = torch.randn(1, 3, 224, 224).to(device)
                
                with torch.no_grad():
                    output = model(dummy_input)
                    probabilities = F.softmax(output, dim=1)
                    predicted_class_id = torch.argmax(output, dim=1).item()
                    predicted_class = class_names[predicted_class_id]
                    confidence = probabilities[0][predicted_class_id].item()
                
                if predicted_class == true_class:
                    correct_predictions += 1
                    class_correct[true_class] += 1
            
            # Calculate metrics
            overall_accuracy = correct_predictions / len(test_samples) * 100
            
            print(f"   🎯 Overall Accuracy: {overall_accuracy:.1f}%")
            print(f"   📈 Per-class accuracy:")
            
            for class_name in class_names:
                if class_total[class_name] > 0:
                    class_acc = class_correct[class_name] / class_total[class_name] * 100
                    print(f"      {class_name:15}: {class_acc:5.1f}% ({class_correct[class_name]}/{class_total[class_name]})")
            
            results[model_name] = {
                'overall_accuracy': overall_accuracy,
                'class_accuracies': {class_name: class_correct[class_name] / class_total[class_name] * 100 
                                   if class_total[class_name] > 0 else 0 
                                   for class_name in class_names}
            }
            
        except Exception as e:
            print(f"   ❌ Error testing {model_name}: {e}")
    
    # Compare results
    if len(results) > 1:
        print(f"\n📊 MODEL COMPARISON SUMMARY")
        print("=" * 50)
        
        for model_name, metrics in results.items():
            print(f"\n{model_name}:")
            print(f"  Overall: {metrics['overall_accuracy']:.1f}%")
            for class_name, acc in metrics['class_accuracies'].items():
                print(f"  {class_name:15}: {acc:5.1f}%")
        
        # Determine which is better
        if "Improved Model" in results and "Original Model" in results:
            improved_acc = results["Improved Model"]["overall_accuracy"]
            original_acc = results["Original Model"]["overall_accuracy"]
            
            if improved_acc > original_acc:
                improvement = improved_acc - original_acc
                print(f"\n🎉 IMPROVED MODEL IS BETTER!")
                print(f"   📈 Improvement: +{improvement:.1f}% accuracy")
            else:
                print(f"\n⚠️  Original model performed better")
    
    return results

def test_inference_with_improved_model():
    """Test inference pipeline with improved model"""
    
    print(f"\n🔮 TESTING IMPROVED MODEL INFERENCE")
    print("=" * 50)
    
    improved_model_path = Path("models/improved_resnet50_model.pth")
    
    if not improved_model_path.exists():
        print("❌ Improved model not found!")
        return False
    
    try:
        # Create predictor with improved model
        predictor = FootDeformityPredictor(
            model_path=improved_model_path,
            model_type='resnet50'
        )
        
        print("✅ Improved model predictor created successfully!")
        
        # Test with sample images from each class
        processed_dataset = Path("processed_dataset")
        class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        
        print(f"\n📸 Testing inference on sample images:")
        
        for class_name in class_names:
            class_dir = processed_dataset / class_name
            if class_dir.exists():
                image_files = list(class_dir.glob("*.jpg"))[:2]  # Test 2 per class
                
                print(f"\n📂 {class_name} class:")
                
                for img_file in image_files:
                    try:
                        result = predictor.predict_single(img_file)
                        
                        predicted_class = result['predicted_class']
                        confidence = result['confidence']
                        correct = predicted_class == class_name
                        
                        print(f"   📸 {img_file.name[:25]}...")
                        print(f"      True: {class_name}, Predicted: {predicted_class}")
                        print(f"      Confidence: {confidence:.1%} {'✅' if correct else '❌'}")
                        
                    except Exception as e:
                        print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing improved model inference: {e}")
        return False

def main():
    """Run comprehensive testing"""
    
    print("🧪 IMPROVED MODEL TESTING")
    print("=" * 60)
    
    # Test 1: Compare models
    comparison_results = compare_models()
    
    # Test 2: Test improved model inference
    inference_success = test_inference_with_improved_model()
    
    print(f"\n🏆 TESTING SUMMARY")
    print("=" * 60)
    
    if comparison_results:
        print("✅ Model comparison completed")
        
        if "Improved Model" in comparison_results:
            improved_acc = comparison_results["Improved Model"]["overall_accuracy"]
            print(f"📊 Improved Model Accuracy: {improved_acc:.1f}%")
            
            if improved_acc > 60:
                print("🎉 Model performance is good!")
            else:
                print("⚠️  Model needs further improvement")
    
    if inference_success:
        print("✅ Inference pipeline working with improved model")
    
    print(f"\n🚀 Next Steps:")
    print(f"   • Wait for training to complete")
    print(f"   • Test final improved model")
    print(f"   • Deploy for production use")

if __name__ == "__main__":
    main()
