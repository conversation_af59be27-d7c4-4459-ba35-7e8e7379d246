"""
Simple Training Script for Foot Deformity Classification
Minimal version to test training functionality
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pandas as pd
from pathlib import Path
import time

# Import our modules
from data_preprocessing import DataPreprocessor, FootDeformityDataset
from model_architectures import create_model

def simple_train():
    """Simple training function"""
    
    print("🚀 Starting Simple Training Test")
    print("=" * 50)
    
    # Check if processed dataset exists
    if not Path("processed_dataset/dataset_manifest.csv").exists():
        print("❌ Processed dataset not found. Please run dataset_analyzer.py first.")
        return
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  Using device: {device}")
    
    # Setup data preprocessing
    print("📊 Loading dataset...")
    preprocessor = DataPreprocessor()
    preprocessor.load_manifest()
    
    # Create data splits
    train_df, val_df, test_df = preprocessor.create_data_splits()
    
    # Create data loaders with smaller batch size
    train_loader, val_loader, test_loader = preprocessor.create_data_loaders(
        train_df, val_df, test_df, 
        batch_size=8,  # Small batch size for stability
        num_workers=0  # No multiprocessing to avoid issues
    )
    
    print(f"📈 Data loaded: {len(train_loader)} train batches")
    
    # Create simple model
    print("🧠 Creating model...")
    model = create_model('resnet50', num_classes=4, pretrained=True)
    model.to(device)
    
    # Simple training setup
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    print("🎯 Starting training...")
    
    # Train for just 2 epochs as a test
    model.train()
    for epoch in range(2):
        print(f"\nEpoch {epoch+1}/2")
        print("-" * 20)
        
        running_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            if batch_idx >= 10:  # Only train on first 10 batches for testing
                break
                
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            if batch_idx % 5 == 0:
                print(f"  Batch {batch_idx:2d} | Loss: {loss.item():.4f} | Acc: {100.*correct/total:.1f}%")
        
        epoch_loss = running_loss / min(10, len(train_loader))
        epoch_acc = 100. * correct / total
        print(f"Epoch {epoch+1} Summary: Loss: {epoch_loss:.4f}, Acc: {epoch_acc:.1f}%")
    
    # Save model
    print("\n💾 Saving model...")
    Path("models").mkdir(exist_ok=True)
    torch.save(model.state_dict(), "models/simple_test_model.pth")
    
    print("✅ Simple training test completed successfully!")
    print("📁 Model saved as 'models/simple_test_model.pth'")
    
    return model

if __name__ == "__main__":
    try:
        model = simple_train()
        print("\n🎉 Training test successful!")
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        import traceback
        traceback.print_exc()
