"""
Fix External Validation for MONAI Medical Model
Comprehensive external validation with robust preprocessing and evaluation
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns
import time
import json

# MONAI imports
import monai
from monai.networks.nets import DenseNet121

class MONAIExternalValidator:
    """External validation for MONAI medical model"""
    
    def __init__(self, model_path="models/monai_densenet121_simple.pth"):
        self.model_path = Path(model_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.model = None
        self.transform = None
        
        print("🏥 MONAI EXTERNAL VALIDATION SYSTEM")
        print("=" * 50)
        print(f"Device: {self.device}")
        print(f"MONAI Version: {monai.__version__}")
    
    def load_model(self):
        """Load the trained MONAI model"""
        
        if not self.model_path.exists():
            raise FileNotFoundError(f"MONAI model not found: {self.model_path}")
        
        print(f"📦 Loading MONAI model from {self.model_path}")
        
        # Create model architecture
        self.model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=False
        )
        
        # Load checkpoint
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        # Get model info
        best_val_acc = checkpoint.get('best_val_acc', 0)
        print(f"✅ MONAI model loaded successfully")
        print(f"📊 Training validation accuracy: {best_val_acc:.1f}%")
        
        # Setup robust preprocessing
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        print("✅ Preprocessing pipeline configured")
    
    def robust_image_preprocessing(self, image_path):
        """Robust image preprocessing with error handling"""
        
        try:
            # Load image with multiple fallback methods
            image = None
            
            # Method 1: PIL with RGB conversion
            try:
                image = Image.open(image_path).convert('RGB')
            except Exception as e:
                print(f"⚠️ PIL RGB failed: {e}")
            
            # Method 2: PIL with RGBA to RGB conversion
            if image is None:
                try:
                    img_rgba = Image.open(image_path).convert('RGBA')
                    # Create white background
                    background = Image.new('RGB', img_rgba.size, (255, 255, 255))
                    background.paste(img_rgba, mask=img_rgba.split()[-1])
                    image = background
                    print("✅ RGBA to RGB conversion successful")
                except Exception as e:
                    print(f"⚠️ RGBA conversion failed: {e}")
            
            # Method 3: Grayscale to RGB conversion
            if image is None:
                try:
                    img_gray = Image.open(image_path).convert('L')
                    image = Image.merge('RGB', (img_gray, img_gray, img_gray))
                    print("✅ Grayscale to RGB conversion successful")
                except Exception as e:
                    print(f"⚠️ Grayscale conversion failed: {e}")
            
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            # Validate image
            if image.size[0] < 32 or image.size[1] < 32:
                print(f"⚠️ Small image detected: {image.size}")
            
            # Apply transforms
            image_tensor = self.transform(image)
            
            # Validate tensor
            if torch.isnan(image_tensor).any():
                print("⚠️ NaN values detected in tensor")
                image_tensor = torch.nan_to_num(image_tensor, nan=0.0)
            
            return image_tensor.unsqueeze(0).to(self.device)
            
        except Exception as e:
            print(f"❌ Image preprocessing failed for {image_path}: {e}")
            return None
    
    def predict_single_image(self, image_path):
        """Predict single image with robust error handling"""
        
        # Preprocess image
        image_tensor = self.robust_image_preprocessing(image_path)
        if image_tensor is None:
            return None
        
        try:
            # Make prediction
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = F.softmax(outputs, dim=1)
                predicted_class_id = torch.argmax(outputs, dim=1).item()
                confidence = probabilities[0][predicted_class_id].item()
            
            return {
                'predicted_class_id': predicted_class_id,
                'predicted_class': self.class_names[predicted_class_id],
                'confidence': confidence,
                'probabilities': probabilities[0].cpu().numpy()
            }
            
        except Exception as e:
            print(f"❌ Prediction failed for {image_path}: {e}")
            return None
    
    def validate_external_dataset(self, external_path):
        """Validate on external dataset with comprehensive error handling"""
        
        external_path = Path(external_path)
        if not external_path.exists():
            print(f"❌ External dataset path not found: {external_path}")
            return None
        
        print(f"\n🔍 EXTERNAL VALIDATION")
        print(f"Dataset path: {external_path}")
        print("-" * 40)
        
        # Find all images
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        all_images = []
        
        for ext in image_extensions:
            all_images.extend(external_path.rglob(f"*{ext}"))
            all_images.extend(external_path.rglob(f"*{ext.upper()}"))
        
        if not all_images:
            print(f"❌ No images found in {external_path}")
            return None
        
        print(f"📊 Found {len(all_images)} external images")
        
        # Process images
        results = []
        successful_predictions = 0
        failed_predictions = 0
        
        for i, image_path in enumerate(all_images):
            if i % 50 == 0:
                print(f"Processing: {i+1}/{len(all_images)} images...")
            
            # Try to infer true label from path
            true_label = self.infer_label_from_path(image_path)
            
            # Make prediction
            prediction = self.predict_single_image(image_path)
            
            if prediction is not None:
                results.append({
                    'image_path': str(image_path),
                    'true_label': true_label,
                    'predicted_class': prediction['predicted_class'],
                    'predicted_class_id': prediction['predicted_class_id'],
                    'confidence': prediction['confidence'],
                    'probabilities': prediction['probabilities']
                })
                successful_predictions += 1
            else:
                failed_predictions += 1
        
        print(f"\n📊 EXTERNAL VALIDATION RESULTS:")
        print(f"   ✅ Successful predictions: {successful_predictions}")
        print(f"   ❌ Failed predictions: {failed_predictions}")
        print(f"   📈 Success rate: {successful_predictions/(successful_predictions+failed_predictions)*100:.1f}%")
        
        if successful_predictions == 0:
            print("❌ No successful predictions made")
            return None
        
        # Analyze results
        return self.analyze_external_results(results)
    
    def infer_label_from_path(self, image_path):
        """Infer true label from image path"""
        
        path_str = str(image_path).lower()
        
        # Check for class names in path
        for i, class_name in enumerate(self.class_names):
            if class_name in path_str:
                return i
        
        # Check for alternative names
        if 'flat' in path_str or 'pes_planus' in path_str:
            return 1  # flatfoot
        elif 'ulcer' in path_str or 'wound' in path_str:
            return 2  # foot_ulcer
        elif 'bunion' in path_str or 'hallux' in path_str:
            return 3  # hallux_valgus
        elif 'normal' in path_str or 'healthy' in path_str:
            return 0  # normal
        
        return -1  # unknown
    
    def analyze_external_results(self, results):
        """Analyze external validation results"""
        
        print(f"\n📊 EXTERNAL VALIDATION ANALYSIS")
        print("=" * 40)
        
        # Overall statistics
        total_predictions = len(results)
        avg_confidence = np.mean([r['confidence'] for r in results])
        
        print(f"Total predictions: {total_predictions}")
        print(f"Average confidence: {avg_confidence:.3f}")
        
        # Confidence distribution
        confidences = [r['confidence'] for r in results]
        high_conf = sum(1 for c in confidences if c > 0.8)
        med_conf = sum(1 for c in confidences if 0.5 <= c <= 0.8)
        low_conf = sum(1 for c in confidences if c < 0.5)
        
        print(f"\nConfidence Distribution:")
        print(f"   High (>80%): {high_conf} ({high_conf/total_predictions*100:.1f}%)")
        print(f"   Medium (50-80%): {med_conf} ({med_conf/total_predictions*100:.1f}%)")
        print(f"   Low (<50%): {low_conf} ({low_conf/total_predictions*100:.1f}%)")
        
        # Class distribution
        class_counts = {}
        for class_name in self.class_names:
            count = sum(1 for r in results if r['predicted_class'] == class_name)
            class_counts[class_name] = count
            print(f"   {class_name}: {count} ({count/total_predictions*100:.1f}%)")
        
        # Accuracy calculation (if true labels available)
        labeled_results = [r for r in results if r['true_label'] != -1]
        if labeled_results:
            print(f"\n🎯 ACCURACY ANALYSIS (Labeled subset: {len(labeled_results)} images)")
            
            true_labels = [r['true_label'] for r in labeled_results]
            predicted_labels = [r['predicted_class_id'] for r in labeled_results]
            
            accuracy = accuracy_score(true_labels, predicted_labels)
            print(f"   External validation accuracy: {accuracy*100:.1f}%")
            
            # Per-class accuracy
            print(f"\nPer-class accuracy:")
            for i, class_name in enumerate(self.class_names):
                class_true = [t for t, p in zip(true_labels, predicted_labels) if t == i]
                class_pred = [p for t, p in zip(true_labels, predicted_labels) if t == i]
                if class_true:
                    class_acc = sum(1 for t, p in zip(class_true, class_pred) if t == p) / len(class_true)
                    print(f"   {class_name}: {class_acc*100:.1f}% ({len(class_true)} samples)")
        
        # Save results
        self.save_external_results(results)
        
        return {
            'total_predictions': total_predictions,
            'average_confidence': avg_confidence,
            'confidence_distribution': {'high': high_conf, 'medium': med_conf, 'low': low_conf},
            'class_distribution': class_counts,
            'labeled_accuracy': accuracy*100 if labeled_results else None,
            'results': results
        }
    
    def save_external_results(self, results):
        """Save external validation results"""
        
        # Create results directory
        results_dir = Path("external_validation_results")
        results_dir.mkdir(exist_ok=True)
        
        # Save detailed results
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"monai_external_validation_{timestamp}.json"
        
        # Convert numpy arrays to lists for JSON serialization
        json_results = []
        for r in results:
            json_result = r.copy()
            json_result['probabilities'] = r['probabilities'].tolist()
            json_results.append(json_result)
        
        with open(results_file, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Create summary report
        summary_file = results_dir / f"monai_validation_summary_{timestamp}.txt"
        with open(summary_file, 'w') as f:
            f.write("MONAI External Validation Summary\n")
            f.write("=" * 40 + "\n")
            f.write(f"Total predictions: {len(results)}\n")
            f.write(f"Average confidence: {np.mean([r['confidence'] for r in results]):.3f}\n")
            f.write(f"Timestamp: {timestamp}\n")
        
        print(f"📄 Summary saved to: {summary_file}")

def main():
    """Main external validation function"""
    
    print("🏥 MONAI EXTERNAL VALIDATION SYSTEM")
    print("=" * 60)
    
    # Initialize validator
    validator = MONAIExternalValidator()
    
    try:
        # Load model
        validator.load_model()
        
        # Test with sample images first
        print("\n🧪 TESTING WITH SAMPLE IMAGES")
        sample_dir = Path("processed_dataset")
        if sample_dir.exists():
            # Test a few sample images
            sample_images = list(sample_dir.rglob("*.jpg"))[:5]
            for img_path in sample_images:
                result = validator.predict_single_image(img_path)
                if result:
                    print(f"✅ {img_path.name}: {result['predicted_class']} ({result['confidence']:.3f})")
                else:
                    print(f"❌ {img_path.name}: Failed")
        
        # External validation
        print("\n🔍 EXTERNAL VALIDATION OPTIONS:")
        print("1. Validate on external dataset directory")
        print("2. Validate on specific image")
        print("3. Skip external validation")
        
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            external_path = input("Enter external dataset path: ").strip()
            if external_path:
                results = validator.validate_external_dataset(external_path)
                if results:
                    print(f"\n🎉 External validation completed successfully!")
                    print(f"   Check 'external_validation_results' directory for detailed results")
        
        elif choice == "2":
            image_path = input("Enter image path: ").strip()
            if image_path and Path(image_path).exists():
                result = validator.predict_single_image(image_path)
                if result:
                    print(f"\n🎯 PREDICTION RESULT:")
                    print(f"   Class: {result['predicted_class']}")
                    print(f"   Confidence: {result['confidence']:.3f}")
                    print(f"   Probabilities: {result['probabilities']}")
                else:
                    print("❌ Prediction failed")
            else:
                print("❌ Invalid image path")
        
        else:
            print("✅ External validation skipped")
    
    except Exception as e:
        print(f"❌ External validation failed: {e}")
        print("\n🔧 TROUBLESHOOTING TIPS:")
        print("1. Check if MONAI model exists: models/monai_densenet121_simple.pth")
        print("2. Ensure external images are in supported formats (jpg, png, etc.)")
        print("3. Verify image paths and permissions")
        print("4. Check if images are corrupted or too small")

if __name__ == "__main__":
    main()
