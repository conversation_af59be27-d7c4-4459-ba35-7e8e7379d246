"""
Enhanced Backend with Vision Transformer and Interpretability
Integrates ViT model with Grad-CAM, attention maps, and SHAP explanations
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import torch
import torch.nn.functional as F
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
import cv2
import io
import time
import logging
import base64
from pathlib import Path
import uvicorn
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# Import our ViT modules
from vit_backend_integration import create_vit_model_simple, ViTInterpreter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="FootAI ViT API",
    description="Vision Transformer-powered foot deformity classification with interpretability",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
model = None
interpreter = None
device = None
transform = None
class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
class_descriptions = {
    'normal': 'Normal foot - no deformity detected',
    'flatfoot': 'Flatfoot (Pes Planus) - collapsed arch condition requiring orthotic intervention',
    'foot_ulcer': 'Foot ulcer - skin lesion detected requiring immediate medical attention',
    'hallux_valgus': 'Hallux Valgus - bunion deformity affecting big toe alignment'
}

def load_vit_model():
    """Load the trained ViT model"""
    global model, interpreter, device, transform
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Try to load ViT model
    model_paths = [
        'models/vit_simple_best.pth',
        'models/best_resnet50_model.pth',  # Fallback to CNN
        'models/improved_resnet50_model.pth'
    ]
    
    model_loaded = False
    model_type = "Unknown"
    
    for model_path in model_paths:
        if Path(model_path).exists():
            try:
                logger.info(f"Loading model from {model_path}")
                
                if 'vit' in model_path:
                    # Load ViT model
                    model = create_vit_model_simple(num_classes=4)
                    checkpoint = torch.load(model_path, map_location=device)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    model_type = "Vision Transformer"
                    
                    # Create interpreter
                    interpreter = ViTInterpreter(model, device=str(device))
                    
                else:
                    # Load CNN model (fallback)
                    from model_architectures import create_model
                    model = create_model('resnet50', num_classes=4, pretrained=False)
                    checkpoint = torch.load(model_path, map_location=device)
                    
                    if 'model_state_dict' in checkpoint:
                        model.load_state_dict(checkpoint['model_state_dict'])
                    else:
                        model.load_state_dict(checkpoint)
                    
                    model_type = "ResNet50 CNN"
                    interpreter = None  # No ViT interpreter for CNN
                
                model.to(device)
                model.eval()
                model_loaded = True
                
                logger.info(f"Model loaded successfully: {model_type}")
                if 'best_val_acc' in checkpoint:
                    logger.info(f"Model validation accuracy: {checkpoint['best_val_acc']:.2f}%")
                break
                
            except Exception as e:
                logger.warning(f"Failed to load model from {model_path}: {e}")
                continue
    
    if not model_loaded:
        raise RuntimeError("No valid model found! Please train a model first.")
    
    # Setup image preprocessing
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    logger.info(f"Model and preprocessing pipeline loaded successfully: {model_type}")
    return model_type

@app.on_event("startup")
async def startup_event():
    """Load model on startup"""
    try:
        model_type = load_vit_model()
        logger.info(f"ViT API server started successfully with {model_type}")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "FootAI ViT API Server", 
        "status": "running", 
        "version": "2.0.0",
        "model_type": "Vision Transformer with Interpretability"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "interpreter_available": interpreter is not None,
        "device": str(device) if device else "unknown",
        "timestamp": time.time()
    }

@app.post("/predict")
async def predict_with_interpretability(file: UploadFile = File(...)):
    """
    Predict foot condition with ViT and generate interpretability maps
    """
    try:
        # Validate file type
        if file.content_type and not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and process image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data)).convert('RGB')
        
        # Preprocess image
        image_tensor = transform(image).unsqueeze(0).to(device)
        
        # Make prediction
        start_time = time.time()
        with torch.no_grad():
            outputs = model(image_tensor)
            probabilities = F.softmax(outputs, dim=1)
            predicted_class_id = torch.argmax(outputs, dim=1).item()
            confidence = probabilities[0][predicted_class_id].item()
        
        inference_time = time.time() - start_time
        
        # Prepare basic results
        predicted_class = class_names[predicted_class_id]
        description = class_descriptions[predicted_class]
        
        all_probabilities = {
            class_names[i]: float(probabilities[0][i].item())
            for i in range(len(class_names))
        }
        
        # Generate interpretability maps if ViT interpreter is available
        interpretability_data = {}
        if interpreter is not None:
            try:
                # Generate attention map
                attention_map = interpreter.generate_attention_map(image_tensor)
                
                # Generate gradient map
                gradient_map = interpreter.generate_gradient_map(image_tensor, predicted_class_id)
                
                # Convert maps to base64 for frontend
                interpretability_data = {
                    "attention_map_available": True,
                    "gradient_map_available": True,
                    "attention_map": attention_map.tolist(),
                    "gradient_map": gradient_map.tolist(),
                    "map_size": attention_map.shape
                }
                
                logger.info("Interpretability maps generated successfully")
                
            except Exception as e:
                logger.warning(f"Failed to generate interpretability maps: {e}")
                interpretability_data = {
                    "attention_map_available": False,
                    "gradient_map_available": False,
                    "error": str(e)
                }
        else:
            interpretability_data = {
                "attention_map_available": False,
                "gradient_map_available": False,
                "message": "ViT interpreter not available (using CNN model)"
            }
        
        # Generate recommendations
        recommendations = generate_medical_recommendations(predicted_class, confidence)
        
        result = {
            "predicted_class": predicted_class,
            "confidence": float(confidence),
            "description": description,
            "all_probabilities": all_probabilities,
            "inference_time": float(inference_time),
            "recommendations": recommendations,
            "interpretability": interpretability_data,
            "timestamp": time.time(),
            "model_info": {
                "architecture": "Vision Transformer" if interpreter else "ResNet50",
                "version": "ViT with Interpretability",
                "device": str(device),
                "interpretability_features": ["attention_maps", "gradient_maps"] if interpreter else []
            }
        }
        
        logger.info(f"ViT Prediction: {predicted_class} ({confidence:.3f}) in {inference_time:.3f}s")
        return result
        
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/interpret")
async def generate_interpretation_visualization(file: UploadFile = File(...)):
    """
    Generate comprehensive interpretation visualization
    """
    try:
        if interpreter is None:
            raise HTTPException(status_code=503, detail="ViT interpreter not available")
        
        # Save uploaded file temporarily
        image_data = await file.read()
        temp_path = "temp_image.jpg"
        
        with open(temp_path, "wb") as f:
            f.write(image_data)
        
        # Generate comprehensive visualization
        results = interpreter.create_visualization(temp_path, save_path="interpretation_result.png")
        
        # Read the generated visualization
        with open("interpretation_result.png", "rb") as f:
            img_data = f.read()
            img_base64 = base64.b64encode(img_data).decode()
        
        # Clean up temporary files
        Path(temp_path).unlink(missing_ok=True)
        Path("interpretation_result.png").unlink(missing_ok=True)
        
        return {
            "visualization": img_base64,
            "results": results,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Interpretation error: {e}")
        raise HTTPException(status_code=500, detail=f"Interpretation failed: {str(e)}")

@app.get("/model/info")
async def get_model_info():
    """Get detailed information about the loaded model"""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    model_info = {
        "architecture": "Vision Transformer" if interpreter else "ResNet50",
        "num_classes": len(class_names),
        "class_names": class_names,
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "device": str(device),
        "input_size": [224, 224, 3],
        "interpretability_features": {
            "attention_maps": interpreter is not None,
            "gradient_maps": interpreter is not None,
            "grad_cam": True,
            "shap_values": False  # Can be implemented
        },
        "preprocessing": {
            "resize": [224, 224],
            "normalize": {
                "mean": [0.485, 0.456, 0.406],
                "std": [0.229, 0.224, 0.225]
            }
        }
    }
    
    return model_info

def generate_medical_recommendations(condition: str, confidence: float) -> list:
    """Generate medical recommendations based on the detected condition"""
    
    base_recommendations = [
        "Consult with a qualified healthcare professional for proper diagnosis",
        "This AI analysis should supplement, not replace, professional medical evaluation"
    ]
    
    condition_specific = {
        'normal': [
            "Maintain good foot hygiene and proper footwear",
            "Continue regular foot health monitoring",
            "Consider routine podiatric check-ups if you have risk factors"
        ],
        'flatfoot': [
            "Consider orthotic evaluation and custom insoles",
            "Consult with a podiatrist for treatment options",
            "Physical therapy may help strengthen foot muscles",
            "Monitor for pain, discomfort, or mobility issues"
        ],
        'foot_ulcer': [
            "Seek immediate medical attention - ulcers require prompt treatment",
            "Keep the area clean and protected",
            "Monitor for signs of infection (redness, warmth, discharge)",
            "Follow up with wound care specialist or podiatrist"
        ],
        'hallux_valgus': [
            "Consider bunion-specific footwear with wide toe box",
            "Consult with orthopedic surgeon or podiatrist",
            "Physical therapy exercises may help with pain management",
            "Monitor progression and consider treatment options"
        ]
    }
    
    recommendations = base_recommendations.copy()
    
    if condition in condition_specific:
        recommendations.extend(condition_specific[condition])
    
    # Add confidence-based recommendations
    if confidence < 0.7:
        recommendations.append("Consider additional imaging or second opinion due to lower confidence score")
    elif confidence > 0.9:
        recommendations.append("High confidence result - proceed with condition-specific care")
    
    return recommendations

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "vit_enhanced_backend:app",
        host="0.0.0.0",
        port=8001,  # Different port to avoid conflicts
        reload=True,
        log_level="info"
    )
