"""
Training Pipeline for Foot Deformity Classification
Handles model training, validation, and optimization
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau, CosineAnnealingLR
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import time
import os
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

from model_architectures import create_model
from data_preprocessing import DataPreprocessor

class FootDeformityTrainer:
    """Training pipeline for foot deformity classification"""
    
    def __init__(self, model_type='resnet50', num_classes=4, device=None):
        self.model_type = model_type
        self.num_classes = num_classes
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training history
        self.train_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
        
        # Best model tracking
        self.best_val_acc = 0.0
        self.best_model_state = None
        
        print(f"🚀 Initializing trainer for {model_type} on {self.device}")
    
    def setup_model(self, pretrained=True, **model_kwargs):
        """Setup the model architecture"""
        
        self.model = create_model(
            model_type=self.model_type,
            num_classes=self.num_classes,
            pretrained=pretrained,
            **model_kwargs
        )
        
        self.model.to(self.device)
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"📊 Model: {self.model_type}")
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        
        return self.model
    
    def setup_training(self, learning_rate=1e-3, weight_decay=1e-4, class_weights=None):
        """Setup optimizer, loss function, and scheduler"""
        
        # Loss function with class weights
        if class_weights is not None:
            # Convert class weights to tensor
            weights = torch.zeros(self.num_classes)
            class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
            for i, class_name in enumerate(class_names):
                if class_name in class_weights:
                    weights[i] = class_weights[class_name]
                else:
                    weights[i] = 1.0
            
            weights = weights.to(self.device)
            self.criterion = nn.CrossEntropyLoss(weight=weights)
            print(f"⚖️  Using weighted loss with weights: {weights.cpu().numpy()}")
        else:
            self.criterion = nn.CrossEntropyLoss()
            print("📊 Using standard CrossEntropyLoss")
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Learning rate scheduler
        self.scheduler = ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=0.5,
            patience=5,
            verbose=True,
            min_lr=1e-7
        )
        
        print(f"🎯 Optimizer: AdamW (lr={learning_rate}, wd={weight_decay})")
        print(f"📈 Scheduler: ReduceLROnPlateau")
    
    def train_epoch(self, train_loader):
        """Train for one epoch"""
        
        self.model.train()
        running_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(self.device), labels.to(self.device)
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            self.optimizer.step()
            
            # Statistics
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
            
            # Print progress
            if batch_idx % 50 == 0:
                print(f'  Batch {batch_idx:3d}/{len(train_loader):3d} | '
                      f'Loss: {loss.item():.4f} | '
                      f'Acc: {100.*correct_predictions/total_samples:.2f}%')
        
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100. * correct_predictions / total_samples
        
        return epoch_loss, epoch_acc
    
    def validate_epoch(self, val_loader):
        """Validate for one epoch"""
        
        self.model.eval()
        running_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                
                outputs = self.model(images)
                loss = self.criterion(outputs, labels)
                
                running_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += labels.size(0)
                correct_predictions += (predicted == labels).sum().item()
        
        epoch_loss = running_loss / len(val_loader)
        epoch_acc = 100. * correct_predictions / total_samples
        
        return epoch_loss, epoch_acc
    
    def train(self, train_loader, val_loader, num_epochs=50, save_dir='models'):
        """Complete training loop"""
        
        print(f"\n🚀 Starting Training for {num_epochs} epochs")
        print("=" * 60)
        
        # Create save directory
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            epoch_start = time.time()
            
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 30)
            
            # Training phase
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # Validation phase
            val_loss, val_acc = self.validate_epoch(val_loader)
            
            # Update learning rate
            self.scheduler.step(val_acc)
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # Save training history
            self.train_history['train_loss'].append(train_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_acc'].append(val_acc)
            self.train_history['learning_rates'].append(current_lr)
            
            # Save best model
            if val_acc > self.best_val_acc:
                self.best_val_acc = val_acc
                self.best_model_state = self.model.state_dict().copy()
                
                # Save checkpoint
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scheduler_state_dict': self.scheduler.state_dict(),
                    'best_val_acc': self.best_val_acc,
                    'train_history': self.train_history
                }
                
                torch.save(checkpoint, save_path / f'best_{self.model_type}_model.pth')
                print(f"💾 New best model saved! Val Acc: {val_acc:.2f}%")
            
            # Print epoch summary
            epoch_time = time.time() - epoch_start
            print(f"\nEpoch Summary:")
            print(f"  Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.2f}%")
            print(f"  Val Loss:   {val_loss:.4f} | Val Acc:   {val_acc:.2f}%")
            print(f"  Learning Rate: {current_lr:.2e}")
            print(f"  Time: {epoch_time:.1f}s")
            
            # Early stopping check
            if current_lr < 1e-7:
                print("🛑 Learning rate too small, stopping training")
                break
        
        total_time = time.time() - start_time
        print(f"\n✅ Training completed in {total_time/60:.1f} minutes")
        print(f"🏆 Best validation accuracy: {self.best_val_acc:.2f}%")
        
        # Load best model
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)
        
        return self.train_history
    
    def plot_training_history(self, save_path='training_history.png'):
        """Plot training history"""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(1, len(self.train_history['train_loss']) + 1)
        
        # Loss plot
        ax1.plot(epochs, self.train_history['train_loss'], 'b-', label='Training Loss')
        ax1.plot(epochs, self.train_history['val_loss'], 'r-', label='Validation Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # Accuracy plot
        ax2.plot(epochs, self.train_history['train_acc'], 'b-', label='Training Accuracy')
        ax2.plot(epochs, self.train_history['val_acc'], 'r-', label='Validation Accuracy')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True)
        
        # Learning rate plot
        ax3.plot(epochs, self.train_history['learning_rates'], 'g-')
        ax3.set_title('Learning Rate Schedule')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_yscale('log')
        ax3.grid(True)
        
        # Best accuracy highlight
        best_epoch = np.argmax(self.train_history['val_acc']) + 1
        best_acc = max(self.train_history['val_acc'])
        
        ax4.plot(epochs, self.train_history['val_acc'], 'r-', linewidth=2)
        ax4.axhline(y=best_acc, color='g', linestyle='--', alpha=0.7)
        ax4.axvline(x=best_epoch, color='g', linestyle='--', alpha=0.7)
        ax4.scatter([best_epoch], [best_acc], color='gold', s=100, zorder=5)
        ax4.set_title(f'Best Validation Accuracy: {best_acc:.2f}% (Epoch {best_epoch})')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Validation Accuracy (%)')
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📊 Training history plot saved as '{save_path}'")

def main():
    """Main training pipeline"""
    
    print("🚀 Foot Deformity Classification Training Pipeline")
    print("=" * 60)
    
    # Setup data preprocessing
    preprocessor = DataPreprocessor()
    preprocessor.load_manifest()
    class_counts, class_weights = preprocessor.analyze_class_distribution()
    
    # Create data splits and loaders
    train_df, val_df, test_df = preprocessor.create_data_splits()
    train_loader, val_loader, test_loader = preprocessor.create_data_loaders(
        train_df, val_df, test_df, batch_size=32
    )
    
    # Initialize trainer
    trainer = FootDeformityTrainer(model_type='resnet50', num_classes=4)
    
    # Setup model and training
    trainer.setup_model(pretrained=True)
    trainer.setup_training(learning_rate=1e-3, class_weights=class_weights)
    
    # Train model
    history = trainer.train(train_loader, val_loader, num_epochs=30)
    
    # Plot results
    trainer.plot_training_history()
    
    print("\n✅ Training pipeline completed successfully!")

if __name__ == "__main__":
    main()
