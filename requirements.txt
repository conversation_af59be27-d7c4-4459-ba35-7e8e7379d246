# Core ML and Deep Learning
torch>=2.0.0
torchvision>=0.15.0
timm>=0.9.0

# Data Processing and Analysis
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
opencv-python>=4.5.0
Pillow>=8.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Progress and Utilities
tqdm>=4.62.0
pathlib2>=2.3.0

# Optional: For faster data loading
# albumentations>=1.3.0

# Optional: For model optimization
# onnx>=1.12.0
# onnxruntime>=1.12.0

# Optional: For experiment tracking
# wandb>=0.13.0
# tensorboard>=2.8.0
