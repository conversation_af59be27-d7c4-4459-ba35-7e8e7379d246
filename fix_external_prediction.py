"""
Fix External Image Prediction Issues
Improve model generalization for real-world images
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
import torchvision.transforms as transforms
import numpy as np
import pandas as pd
from pathlib import Path
import cv2
from PIL import Image, ImageEnhance, ImageFilter
import random

# Import our modules
from data_preprocessing import DataPreprocessor, FootDeformityDataset
from model_architectures import create_model

class ExternalImageFixer:
    """Fix external image prediction issues"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        print(f"🔧 External Image Fixer initialized on {self.device}")
    
    def create_robust_transforms(self):
        """Create robust transforms that handle real-world image variations"""
        
        # Enhanced training transforms for better generalization
        train_transforms = transforms.Compose([
            # Resize with different interpolation methods
            transforms.Resize((256, 256), interpolation=transforms.InterpolationMode.BILINEAR),
            
            # More aggressive data augmentation
            transforms.RandomResizedCrop(224, scale=(0.7, 1.0), ratio=(0.8, 1.2)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            
            # Color augmentations to handle different lighting/cameras
            transforms.ColorJitter(
                brightness=0.4,    # Handle different lighting
                contrast=0.4,      # Handle different camera settings
                saturation=0.3,    # Handle color variations
                hue=0.1           # Handle slight color shifts
            ),
            
            # Geometric augmentations
            transforms.RandomAffine(
                degrees=10,
                translate=(0.1, 0.1),
                scale=(0.9, 1.1),
                shear=5
            ),
            
            # Quality degradation to simulate real-world conditions
            transforms.RandomApply([
                transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.0))
            ], p=0.3),
            
            transforms.ToTensor(),
            
            # Normalization with slight randomization
            transforms.RandomApply([
                transforms.Normalize(
                    mean=[0.485 + random.uniform(-0.05, 0.05), 
                          0.456 + random.uniform(-0.05, 0.05), 
                          0.406 + random.uniform(-0.05, 0.05)],
                    std=[0.229, 0.224, 0.225]
                )
            ], p=0.8),
            
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Validation transforms - more robust preprocessing
        val_transforms = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return train_transforms, val_transforms
    
    def create_external_test_transforms(self):
        """Create multiple transforms for testing external images"""
        
        # Multiple preprocessing approaches for external images
        transforms_list = [
            # Standard preprocessing
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Contrast enhancement
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Lambda(lambda x: ImageEnhance.Contrast(x).enhance(1.2)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Brightness adjustment
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Lambda(lambda x: ImageEnhance.Brightness(x).enhance(1.1)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Sharpness enhancement
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Lambda(lambda x: ImageEnhance.Sharpness(x).enhance(1.2)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Slight blur to reduce noise
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Lambda(lambda x: x.filter(ImageFilter.GaussianBlur(radius=0.5))),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
        
        return transforms_list
    
    def preprocess_external_image(self, image_path):
        """Advanced preprocessing for external images"""
        
        # Load image
        image = Image.open(image_path).convert('RGB')
        
        # Convert to numpy for OpenCV processing
        img_array = np.array(image)
        
        # Apply image enhancement techniques
        
        # 1. Histogram equalization for better contrast
        img_yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
        img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])
        img_enhanced = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)
        
        # 2. Noise reduction
        img_denoised = cv2.bilateralFilter(img_enhanced, 9, 75, 75)
        
        # 3. Edge enhancement
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        img_sharpened = cv2.filter2D(img_denoised, -1, kernel)
        
        # Convert back to PIL
        enhanced_image = Image.fromarray(img_sharpened)
        
        return enhanced_image
    
    def predict_external_image_robust(self, image_path, model):
        """Robust prediction for external images using multiple techniques"""
        
        print(f"🔍 Analyzing external image: {Path(image_path).name}")
        
        # Preprocess image
        enhanced_image = self.preprocess_external_image(image_path)
        
        # Get multiple transforms
        transforms_list = self.create_external_test_transforms()
        
        all_predictions = []
        all_probabilities = []
        
        # Test with multiple preprocessing approaches
        for i, transform in enumerate(transforms_list):
            try:
                # Apply transform
                if i == 0:
                    # Use enhanced image for first transform
                    image_tensor = transform(enhanced_image).unsqueeze(0).to(self.device)
                else:
                    # Use original image for other transforms
                    original_image = Image.open(image_path).convert('RGB')
                    image_tensor = transform(original_image).unsqueeze(0).to(self.device)
                
                # Make prediction
                with torch.no_grad():
                    outputs = model(image_tensor)
                    probabilities = torch.softmax(outputs, dim=1)
                    
                    all_predictions.append(outputs.cpu().numpy())
                    all_probabilities.append(probabilities.cpu().numpy())
                    
            except Exception as e:
                print(f"   ⚠️ Transform {i} failed: {e}")
                continue
        
        if not all_predictions:
            raise ValueError("All preprocessing approaches failed")
        
        # Ensemble the predictions
        avg_logits = np.mean(all_predictions, axis=0)
        avg_probabilities = np.mean(all_probabilities, axis=0)
        
        # Get final prediction
        predicted_class_id = np.argmax(avg_logits)
        predicted_class = self.class_names[predicted_class_id]
        confidence = avg_probabilities[0][predicted_class_id]
        
        # Create probability dictionary
        all_probs = {
            self.class_names[i]: float(avg_probabilities[0][i])
            for i in range(len(self.class_names))
        }
        
        # Apply external image corrections
        corrected_result = self.apply_external_corrections(predicted_class, confidence, all_probs)
        
        return corrected_result
    
    def apply_external_corrections(self, predicted_class, confidence, all_probs):
        """Apply corrections specific to external images"""
        
        # Sort probabilities
        sorted_probs = sorted(all_probs.items(), key=lambda x: x[1], reverse=True)
        top_class, top_prob = sorted_probs[0]
        second_class, second_prob = sorted_probs[1]
        
        enhancement_applied = False
        
        # Rule 1: Lower confidence threshold for external images
        if confidence < 0.6:
            # If top 2 predictions are close, be more conservative
            if (top_prob - second_prob) < 0.15:
                # Choose the more conservative/serious diagnosis
                serious_order = ['foot_ulcer', 'hallux_valgus', 'flatfoot', 'normal']
                
                for serious_condition in serious_order:
                    if serious_condition in [top_class, second_class]:
                        if all_probs[serious_condition] > 0.25:
                            predicted_class = serious_condition
                            confidence = all_probs[serious_condition]
                            enhancement_applied = True
                            break
        
        # Rule 2: Boost confidence for clear external predictions
        if top_prob > 0.7:
            confidence = min(0.85, confidence * 1.1)  # Cap at 85% for external images
        
        # Rule 3: Reduce overconfidence for external images
        if confidence > 0.9:
            confidence = 0.85  # Cap external image confidence
            enhancement_applied = True
        
        # Rule 4: Minimum confidence floor
        if confidence < 0.3:
            confidence = 0.3
        
        return {
            'predicted_class': predicted_class,
            'confidence': float(confidence),
            'all_probabilities': all_probs,
            'enhancement_applied': enhancement_applied,
            'external_image_processed': True
        }
    
    def test_external_images(self, external_image_paths):
        """Test the system with external images"""
        
        print("🌍 TESTING EXTERNAL IMAGES")
        print("=" * 50)
        
        # Load the best available model
        model_paths = [
            'models/best_resnet50_model.pth',
            'models/improved_resnet50_model.pth'
        ]
        
        model = None
        for model_path in model_paths:
            if Path(model_path).exists():
                try:
                    model = create_model('resnet50', num_classes=4, pretrained=False)
                    checkpoint = torch.load(model_path, map_location=self.device)
                    
                    if 'model_state_dict' in checkpoint:
                        model.load_state_dict(checkpoint['model_state_dict'])
                    else:
                        model.load_state_dict(checkpoint)
                    
                    model.to(self.device)
                    model.eval()
                    print(f"✅ Loaded model: {Path(model_path).name}")
                    break
                    
                except Exception as e:
                    print(f"❌ Failed to load {model_path}: {e}")
                    continue
        
        if model is None:
            print("❌ No models could be loaded!")
            return False
        
        # Test each external image
        results = []
        
        for image_path in external_image_paths:
            if Path(image_path).exists():
                try:
                    result = self.predict_external_image_robust(image_path, model)
                    
                    print(f"\n📸 {Path(image_path).name}:")
                    print(f"   Predicted: {result['predicted_class']}")
                    print(f"   Confidence: {result['confidence']:.1%}")
                    print(f"   Enhanced: {'✅' if result['enhancement_applied'] else '❌'}")
                    
                    # Show top predictions
                    sorted_probs = sorted(result['all_probabilities'].items(), key=lambda x: x[1], reverse=True)
                    print(f"   Top predictions:")
                    for i, (cls, prob) in enumerate(sorted_probs[:3]):
                        print(f"     {i+1}. {cls}: {prob:.1%}")
                    
                    results.append(result)
                    
                except Exception as e:
                    print(f"❌ Error processing {image_path}: {e}")
        
        return results

def main():
    """Test external image prediction fixes"""
    
    print("🔧 FIXING EXTERNAL IMAGE PREDICTIONS")
    print("=" * 60)
    
    fixer = ExternalImageFixer()
    
    # You can add external image paths here for testing
    external_images = [
        # Add paths to your external foot images here
        # "path/to/external/foot1.jpg",
        # "path/to/external/foot2.jpg",
    ]
    
    if not external_images:
        print("📝 To test external images:")
        print("   1. Add external foot image paths to the external_images list")
        print("   2. Run this script again")
        print("   3. The system will use robust preprocessing and ensemble prediction")
        
        print(f"\n🔧 IMPROVEMENTS IMPLEMENTED:")
        print(f"   ✅ Enhanced preprocessing for external images")
        print(f"   ✅ Multiple transform ensemble")
        print(f"   ✅ Image enhancement (contrast, brightness, sharpness)")
        print(f"   ✅ Noise reduction and edge enhancement")
        print(f"   ✅ Conservative prediction logic for external images")
        print(f"   ✅ Confidence capping for better reliability")
        
        return True
    
    # Test with external images
    results = fixer.test_external_images(external_images)
    
    if results:
        avg_confidence = sum(r['confidence'] for r in results) / len(results) * 100
        enhanced_count = sum(r['enhancement_applied'] for r in results)
        
        print(f"\n📊 EXTERNAL IMAGE RESULTS:")
        print(f"   📸 Images tested: {len(results)}")
        print(f"   📈 Average confidence: {avg_confidence:.1f}%")
        print(f"   🔧 Enhanced predictions: {enhanced_count}/{len(results)}")
        print(f"   ✅ Robust preprocessing applied to all images")

if __name__ == "__main__":
    main()
