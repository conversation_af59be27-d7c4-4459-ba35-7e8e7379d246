"""
Enhanced Prediction Module with Better Accuracy
Uses multiple techniques to improve prediction accuracy
"""

import torch
import torch.nn.functional as F
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
from pathlib import Path

# Import our modules
from model_architectures import create_model

class EnhancedPredictor:
    """Enhanced predictor with multiple techniques for better accuracy"""
    
    def __init__(self, model_path=None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.class_descriptions = {
            'normal': 'Normal foot - no deformity detected',
            'flatfoot': 'Flatfoot (Pes Planus) - collapsed arch condition requiring orthotic intervention',
            'foot_ulcer': 'Foot ulcer - skin lesion detected requiring immediate medical attention',
            'hallux_valgus': 'Hallux Valgus - bunion deformity affecting big toe alignment'
        }
        
        # Load models
        self.models = self.load_multiple_models()
        self.transforms = self.create_enhanced_transforms()
        
        print(f"🚀 Enhanced Predictor initialized with {len(self.models)} models on {self.device}")
    
    def load_multiple_models(self):
        """Load multiple models for ensemble prediction"""
        
        models = []
        model_paths = [
            'models/best_resnet50_model.pth',
            'models/improved_resnet50_model.pth'
        ]
        
        for model_path in model_paths:
            if Path(model_path).exists():
                try:
                    model = create_model('resnet50', num_classes=4, pretrained=False)
                    
                    checkpoint = torch.load(model_path, map_location=self.device)
                    if 'model_state_dict' in checkpoint:
                        model.load_state_dict(checkpoint['model_state_dict'])
                    else:
                        model.load_state_dict(checkpoint)
                    
                    model.to(self.device)
                    model.eval()
                    models.append((model, model_path))
                    print(f"✅ Loaded model: {Path(model_path).name}")
                    
                except Exception as e:
                    print(f"❌ Failed to load {model_path}: {e}")
        
        if not models:
            raise RuntimeError("No models could be loaded!")
        
        return models
    
    def create_enhanced_transforms(self):
        """Create multiple transforms for test-time augmentation"""
        
        base_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Test-time augmentation transforms
        tta_transforms = [
            # Original
            base_transform,
            
            # Horizontal flip
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(p=1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Slight rotation
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomRotation(degrees=5),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            
            # Brightness adjustment
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ColorJitter(brightness=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
        
        return tta_transforms
    
    def predict_enhanced(self, image_path):
        """Enhanced prediction with ensemble and TTA"""
        
        # Load image
        image = Image.open(image_path).convert('RGB')
        
        all_predictions = []
        all_probabilities = []
        
        # Test-time augmentation
        for transform in self.transforms:
            image_tensor = transform(image).unsqueeze(0).to(self.device)
            
            # Ensemble prediction
            for model, model_path in self.models:
                with torch.no_grad():
                    outputs = model(image_tensor)
                    probabilities = F.softmax(outputs, dim=1)
                    
                    all_predictions.append(outputs.cpu().numpy())
                    all_probabilities.append(probabilities.cpu().numpy())
        
        # Average all predictions
        avg_logits = np.mean(all_predictions, axis=0)
        avg_probabilities = np.mean(all_probabilities, axis=0)
        
        # Get final prediction
        predicted_class_id = np.argmax(avg_logits)
        predicted_class = self.class_names[predicted_class_id]
        confidence = avg_probabilities[0][predicted_class_id]
        
        # Create probability dictionary
        all_probs = {
            self.class_names[i]: float(avg_probabilities[0][i])
            for i in range(len(self.class_names))
        }
        
        # Apply medical logic corrections
        corrected_result = self.apply_medical_corrections(predicted_class, confidence, all_probs)
        
        return {
            'predicted_class': corrected_result['predicted_class'],
            'confidence': corrected_result['confidence'],
            'description': self.class_descriptions[corrected_result['predicted_class']],
            'all_probabilities': corrected_result['all_probabilities'],
            'raw_prediction': predicted_class,
            'raw_confidence': float(confidence),
            'enhancement_applied': corrected_result.get('enhancement_applied', False)
        }
    
    def apply_medical_corrections(self, predicted_class, confidence, all_probs):
        """Apply medical logic to improve predictions"""
        
        # Sort probabilities
        sorted_probs = sorted(all_probs.items(), key=lambda x: x[1], reverse=True)
        top_class, top_prob = sorted_probs[0]
        second_class, second_prob = sorted_probs[1]
        
        # Medical correction rules
        enhancement_applied = False
        
        # Rule 1: If confidence is low and top 2 are close, be more conservative
        if confidence < 0.7 and (top_prob - second_prob) < 0.2:
            # If one of top 2 is a serious condition, prefer it
            serious_conditions = ['foot_ulcer', 'hallux_valgus']
            
            if top_class in serious_conditions or second_class in serious_conditions:
                serious_class = top_class if top_class in serious_conditions else second_class
                serious_prob = all_probs[serious_class]
                
                if serious_prob > 0.3:  # Reasonable threshold for serious conditions
                    predicted_class = serious_class
                    confidence = serious_prob
                    enhancement_applied = True
        
        # Rule 2: Flatfoot vs Normal distinction (common confusion)
        if set([top_class, second_class]) == set(['normal', 'flatfoot']):
            # If flatfoot probability is significant, prefer it (conservative medical approach)
            if all_probs['flatfoot'] > 0.4:
                predicted_class = 'flatfoot'
                confidence = all_probs['flatfoot']
                enhancement_applied = True
        
        # Rule 3: Boost confidence for clear cases
        if top_prob > 0.8:
            confidence = min(0.95, top_prob * 1.1)  # Slight boost, cap at 95%
        
        # Rule 4: Lower confidence for uncertain cases
        if top_prob < 0.5:
            confidence = max(0.3, top_prob * 0.9)  # Slight reduction, floor at 30%
        
        return {
            'predicted_class': predicted_class,
            'confidence': float(confidence),
            'all_probabilities': all_probs,
            'enhancement_applied': enhancement_applied
        }

def test_enhanced_predictor():
    """Test the enhanced predictor"""
    
    print("🧪 Testing Enhanced Predictor")
    print("=" * 40)
    
    try:
        predictor = EnhancedPredictor()
        
        # Test with sample images
        processed_dataset = Path("processed_dataset")
        test_results = []
        
        for class_name in ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']:
            class_dir = processed_dataset / class_name
            if class_dir.exists():
                image_files = list(class_dir.glob("*.jpg"))
                if image_files:
                    test_image = image_files[0]
                    
                    print(f"\n📸 Testing {class_name} image: {test_image.name}")
                    
                    result = predictor.predict_enhanced(test_image)
                    
                    correct = result['predicted_class'] == class_name
                    enhanced = result.get('enhancement_applied', False)
                    
                    print(f"   True: {class_name}")
                    print(f"   Predicted: {result['predicted_class']}")
                    print(f"   Confidence: {result['confidence']:.1%}")
                    print(f"   Correct: {'✅' if correct else '❌'}")
                    print(f"   Enhanced: {'✅' if enhanced else '❌'}")
                    
                    if result['raw_prediction'] != result['predicted_class']:
                        print(f"   Raw prediction: {result['raw_prediction']} → {result['predicted_class']}")
                    
                    test_results.append({
                        'true_class': class_name,
                        'predicted_class': result['predicted_class'],
                        'correct': correct,
                        'confidence': result['confidence'],
                        'enhanced': enhanced
                    })
        
        # Summary
        if test_results:
            accuracy = sum(r['correct'] for r in test_results) / len(test_results) * 100
            enhanced_count = sum(r['enhanced'] for r in test_results)
            avg_confidence = sum(r['confidence'] for r in test_results) / len(test_results) * 100
            
            print(f"\n📊 Enhanced Predictor Results:")
            print(f"   🎯 Accuracy: {accuracy:.1f}% ({sum(r['correct'] for r in test_results)}/{len(test_results)})")
            print(f"   🔧 Enhanced predictions: {enhanced_count}/{len(test_results)}")
            print(f"   📈 Average confidence: {avg_confidence:.1f}%")
            
            if accuracy > 75:
                print(f"   ✅ Good performance achieved!")
            else:
                print(f"   ⚠️  Performance needs improvement")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced predictor test failed: {e}")
        return False

if __name__ == "__main__":
    test_enhanced_predictor()
