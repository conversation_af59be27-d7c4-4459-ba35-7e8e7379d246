import*as e from"react";import{useSyncExternalStore as t}from'./useSyncExternalStoreShimClient.js';import{useSyncExternalStore as o}from'./useSyncExternalStoreShimServer.js';const r=typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.document.createElement!="undefined",s=!r,c=s?o:t,a="useSyncExternalStore"in e?(n=>n.useSyncExternalStore)(e):c;export{a as useSyncExternalStore};
