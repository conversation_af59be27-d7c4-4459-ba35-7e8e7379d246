"""
Fix PyTorch Model Loading Issues
Resolve weights_only compatibility for ViT and other models
"""

import torch
import numpy as np
from pathlib import Path

def fix_model_loading():
    """Fix model loading with PyTorch 2.6 compatibility"""
    
    print("🔧 FIXING MODEL LOADING ISSUES")
    print("=" * 40)
    
    # Add safe globals for numpy
    torch.serialization.add_safe_globals([np.core.multiarray._reconstruct])
    
    models_dir = Path("models")
    model_files = [
        "vit_simple_best.pth",
        "improved_resnet50_model.pth"
    ]
    
    for model_file in model_files:
        model_path = models_dir / model_file
        if model_path.exists():
            try:
                print(f"🔄 Fixing {model_file}...")
                
                # Load with weights_only=False (trusted source)
                checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
                
                # Re-save with compatible format
                fixed_path = models_dir / f"fixed_{model_file}"
                torch.save(checkpoint, fixed_path)
                
                # Test loading the fixed version
                test_checkpoint = torch.load(fixed_path, map_location='cpu')
                val_acc = test_checkpoint.get('best_val_acc', 0)
                
                print(f"✅ Fixed {model_file}: {val_acc:.1f}% accuracy")
                
            except Exception as e:
                print(f"❌ Failed to fix {model_file}: {e}")
        else:
            print(f"❌ {model_file} not found")

def test_fixed_models():
    """Test the fixed models"""
    
    print(f"\n🧪 TESTING FIXED MODELS")
    print("-" * 30)
    
    models_dir = Path("models")
    fixed_models = list(models_dir.glob("fixed_*.pth"))
    
    for model_path in fixed_models:
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            val_acc = checkpoint.get('best_val_acc', 0)
            epoch = checkpoint.get('epoch', 'Unknown')
            
            print(f"✅ {model_path.name}: {val_acc:.1f}% (Epoch {epoch})")
            
        except Exception as e:
            print(f"❌ {model_path.name}: {e}")

if __name__ == "__main__":
    fix_model_loading()
    test_fixed_models()
    
    print(f"\n🎉 MODEL LOADING FIXES COMPLETE!")
    print(f"   Use fixed_*.pth files for ViT and other models")
