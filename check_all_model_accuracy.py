"""
Comprehensive Model Accuracy Checker
Check accuracy of all trained models for Hallux Valgus detection
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.models as models
import pandas as pd
import numpy as np
from pathlib import Path
from PIL import Image
import time
import json
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# MONAI imports
try:
    import monai
    from monai.networks.nets import DenseNet121, EfficientNetBN
    MONAI_AVAILABLE = True
except ImportError:
    MONAI_AVAILABLE = False

# Vision Transformer
try:
    import timm
    VIT_AVAILABLE = True
except ImportError:
    VIT_AVAILABLE = False

class ModelAccuracyChecker:
    """Comprehensive accuracy checker for all trained models"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.num_classes = len(self.class_names)
        self.results = {}
        
        print(f"🔍 COMPREHENSIVE MODEL ACCURACY CHECKER")
        print(f"=" * 70)
        print(f"Device: {self.device}")
        print(f"Classes: {self.num_classes}")
        if MONAI_AVAILABLE:
            print(f"MONAI Version: {monai.__version__}")
        if VIT_AVAILABLE:
            print(f"TIMM Available: ✅")
    
    def load_validation_data(self):
        """Load validation dataset"""
        
        print(f"\n📊 LOADING VALIDATION DATA")
        print("-" * 50)
        
        # Load dataset
        manifest_path = Path("processed_dataset/dataset_manifest.csv")
        if not manifest_path.exists():
            raise FileNotFoundError("Dataset manifest not found")
        
        df = pd.read_csv(manifest_path)
        
        # Use same split as training (80/20)
        from sklearn.model_selection import train_test_split
        train_df, val_df = train_test_split(
            df, test_size=0.2, stratify=df['class_id'], random_state=42
        )
        
        print(f"Validation samples: {len(val_df)}")
        
        # Class distribution in validation
        val_class_counts = val_df['class'].value_counts()
        print("Validation class distribution:")
        for class_name, count in val_class_counts.items():
            print(f"  {class_name}: {count}")
        
        # Create validation transform
        val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Create validation dataset and loader
        val_dataset = FootDataset(val_df, val_transform)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=0)
        
        return val_loader
    
    def create_model_from_checkpoint(self, model_path):
        """Create model architecture based on checkpoint"""
        
        try:
            # Load checkpoint to check model info
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            
            # Determine model type from filename and checkpoint
            model_name = Path(model_path).stem.lower()
            
            if 'monai' in model_name or 'densenet' in model_name:
                if MONAI_AVAILABLE:
                    model = DenseNet121(
                        spatial_dims=2,
                        in_channels=3,
                        out_channels=self.num_classes,
                        pretrained=False
                    )
                    # Check if it has dropout layer (specialist model)
                    if 'hallux_valgus' in model_name:
                        original_classifier = model.class_layers.out
                        model.class_layers.out = torch.nn.Sequential(
                            torch.nn.Dropout(0.3),
                            original_classifier
                        )
                else:
                    return None, "MONAI not available"
            
            elif 'resnet' in model_name:
                model = models.resnet50(weights=None)
                model.fc = torch.nn.Linear(model.fc.in_features, self.num_classes)
            
            elif 'vit' in model_name:
                if VIT_AVAILABLE:
                    model = timm.create_model('vit_base_patch16_224', pretrained=False, num_classes=self.num_classes)
                else:
                    return None, "TIMM not available"
            
            elif 'efficientnet' in model_name:
                model = models.efficientnet_b3(weights=None)
                model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, self.num_classes)
            
            elif 'inception' in model_name:
                model = models.inception_v3(weights=None)
                model.aux_logits = False
                model.fc = torch.nn.Linear(model.fc.in_features, self.num_classes)
            
            else:
                # Try to infer from checkpoint structure
                if MONAI_AVAILABLE:
                    model = DenseNet121(
                        spatial_dims=2,
                        in_channels=3,
                        out_channels=self.num_classes,
                        pretrained=False
                    )
                else:
                    return None, "Unknown model type"
            
            # Load state dict
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            model.to(self.device)
            model.eval()
            
            return model, "Success"
            
        except Exception as e:
            return None, str(e)
    
    def evaluate_model(self, model, model_name, val_loader):
        """Evaluate a single model"""
        
        print(f"\n🔍 EVALUATING {model_name.upper()}")
        print("-" * 50)
        
        model.eval()
        all_predictions = []
        all_labels = []
        all_probabilities = []
        correct = 0
        total = 0
        
        start_time = time.time()
        
        with torch.no_grad():
            for batch_idx, (inputs, labels) in enumerate(val_loader):
                try:
                    inputs, labels = inputs.to(self.device), labels.to(self.device)
                    outputs = model(inputs)
                    probabilities = F.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs, 1)
                    
                    total += labels.size(0)
                    correct += (predicted == labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
                    all_probabilities.extend(probabilities.cpu().numpy())
                    
                    if batch_idx % 10 == 0:
                        current_acc = 100. * correct / total if total > 0 else 0
                        print(f"  Batch {batch_idx:2d} | Acc: {current_acc:.1f}%")
                
                except Exception as e:
                    print(f"⚠️ Batch {batch_idx} error: {e}")
                    continue
        
        inference_time = time.time() - start_time
        
        if total == 0:
            return None
        
        # Calculate overall accuracy
        overall_accuracy = 100. * correct / total
        
        # Calculate per-class metrics
        class_metrics = {}
        for i, class_name in enumerate(self.class_names):
            class_mask = np.array(all_labels) == i
            if class_mask.sum() > 0:
                class_predictions = np.array(all_predictions)[class_mask]
                class_labels = np.array(all_labels)[class_mask]
                class_correct = (class_predictions == class_labels).sum()
                class_accuracy = 100. * class_correct / class_mask.sum()
                class_metrics[class_name] = {
                    'accuracy': class_accuracy,
                    'correct': int(class_correct),
                    'total': int(class_mask.sum())
                }
        
        # Hallux Valgus specific metrics
        hv_mask = np.array(all_labels) == 3
        hv_recall = 0.0
        hv_precision = 0.0
        hv_f1 = 0.0
        
        if hv_mask.sum() > 0:
            hv_correct = (np.array(all_predictions)[hv_mask] == 3).sum()
            hv_recall = 100. * hv_correct / hv_mask.sum()
            
            hv_predicted_mask = np.array(all_predictions) == 3
            if hv_predicted_mask.sum() > 0:
                hv_precision = 100. * hv_correct / hv_predicted_mask.sum()
                
                if hv_precision > 0 and hv_recall > 0:
                    hv_f1 = 2 * (hv_precision * hv_recall) / (hv_precision + hv_recall)
        
        print(f"\n📊 {model_name} RESULTS:")
        print(f"   Overall Accuracy: {overall_accuracy:.1f}%")
        print(f"   🦶 Hallux Valgus Recall: {hv_recall:.1f}%")
        print(f"   🦶 Hallux Valgus Precision: {hv_precision:.1f}%")
        print(f"   🦶 Hallux Valgus F1-Score: {hv_f1:.1f}%")
        print(f"   ⏱️ Inference Time: {inference_time:.2f}s")
        
        # Per-class breakdown
        print(f"\n   Per-Class Accuracy:")
        for class_name, metrics in class_metrics.items():
            print(f"     {class_name:15}: {metrics['accuracy']:5.1f}% ({metrics['correct']}/{metrics['total']})")
        
        return {
            'overall_accuracy': overall_accuracy,
            'hallux_valgus_recall': hv_recall,
            'hallux_valgus_precision': hv_precision,
            'hallux_valgus_f1': hv_f1,
            'class_metrics': class_metrics,
            'inference_time': inference_time,
            'total_samples': total,
            'predictions': all_predictions,
            'labels': all_labels,
            'probabilities': all_probabilities
        }
    
    def check_all_models(self):
        """Check accuracy of all available models"""
        
        print(f"\n🔍 CHECKING ALL MODEL ACCURACIES")
        print("=" * 70)
        
        # Load validation data
        val_loader = self.load_validation_data()
        
        # Find all model files
        models_dir = Path("models")
        if not models_dir.exists():
            print("❌ Models directory not found")
            return
        
        model_files = list(models_dir.glob("*.pth"))
        print(f"\n📁 Found {len(model_files)} model files")
        
        # Evaluate each model
        for model_file in model_files:
            model_name = model_file.stem
            print(f"\n🔄 Loading {model_name}...")
            
            try:
                model, status = self.create_model_from_checkpoint(model_file)
                
                if model is None:
                    print(f"❌ Failed to load {model_name}: {status}")
                    continue
                
                # Evaluate model
                results = self.evaluate_model(model, model_name, val_loader)
                
                if results is not None:
                    self.results[model_name] = results
                    print(f"✅ {model_name} evaluation completed")
                else:
                    print(f"❌ {model_name} evaluation failed")
                
                # Clean up memory
                del model
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
            except Exception as e:
                print(f"❌ Error evaluating {model_name}: {e}")
                continue
        
        # Generate comparison report
        self.generate_accuracy_report()
    
    def generate_accuracy_report(self):
        """Generate comprehensive accuracy comparison report"""
        
        print(f"\n📊 COMPREHENSIVE MODEL ACCURACY REPORT")
        print("=" * 70)
        
        if not self.results:
            print("❌ No model results to compare")
            return
        
        # Create comparison table
        comparison_data = []
        for model_name, results in self.results.items():
            comparison_data.append({
                'Model': model_name,
                'Overall_Acc': f"{results['overall_accuracy']:.1f}%",
                'HV_Recall': f"{results['hallux_valgus_recall']:.1f}%",
                'HV_Precision': f"{results['hallux_valgus_precision']:.1f}%",
                'HV_F1': f"{results['hallux_valgus_f1']:.1f}%",
                'Inference_Time': f"{results['inference_time']:.2f}s",
                'Samples': results['total_samples']
            })
        
        # Sort by Hallux Valgus recall (most important metric)
        comparison_data.sort(key=lambda x: float(x['HV_Recall'].replace('%', '')), reverse=True)
        
        print("\n🏆 MODEL RANKING (by Hallux Valgus Recall):")
        print("-" * 100)
        print(f"{'Rank':<4} {'Model':<25} {'Overall':<8} {'HV Recall':<10} {'HV Prec':<9} {'HV F1':<7} {'Time':<8}")
        print("-" * 100)
        
        for i, data in enumerate(comparison_data, 1):
            print(f"{i:<4} {data['Model']:<25} {data['Overall_Acc']:<8} {data['HV_Recall']:<10} {data['HV_Precision']:<9} {data['HV_F1']:<7} {data['Inference_Time']:<8}")
        
        # Best model analysis
        if comparison_data:
            best_model = comparison_data[0]
            print(f"\n🥇 BEST MODEL FOR HALLUX VALGUS DETECTION:")
            print(f"   Model: {best_model['Model']}")
            print(f"   🎯 Hallux Valgus Recall: {best_model['HV_Recall']}")
            print(f"   📊 Overall Accuracy: {best_model['Overall_Acc']}")
            print(f"   🎯 Hallux Valgus Precision: {best_model['HV_Precision']}")
            print(f"   🎯 Hallux Valgus F1-Score: {best_model['HV_F1']}")
            print(f"   ⏱️ Inference Time: {best_model['Inference_Time']}")
        
        # Save detailed results
        results_file = f"all_model_accuracy_results_{int(time.time())}.json"
        with open(results_file, 'w') as f:
            # Convert numpy arrays to lists for JSON serialization
            json_results = {}
            for model_name, results in self.results.items():
                json_results[model_name] = {
                    'overall_accuracy': results['overall_accuracy'],
                    'hallux_valgus_recall': results['hallux_valgus_recall'],
                    'hallux_valgus_precision': results['hallux_valgus_precision'],
                    'hallux_valgus_f1': results['hallux_valgus_f1'],
                    'class_metrics': results['class_metrics'],
                    'inference_time': results['inference_time'],
                    'total_samples': results['total_samples']
                }
            
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        # Performance categories
        print(f"\n📈 PERFORMANCE CATEGORIES:")
        excellent_models = [d for d in comparison_data if float(d['HV_Recall'].replace('%', '')) >= 90]
        good_models = [d for d in comparison_data if 80 <= float(d['HV_Recall'].replace('%', '')) < 90]
        fair_models = [d for d in comparison_data if 70 <= float(d['HV_Recall'].replace('%', '')) < 80]
        
        if excellent_models:
            print(f"   🏆 Excellent (≥90% HV Recall): {len(excellent_models)} models")
            for model in excellent_models:
                print(f"     - {model['Model']}: {model['HV_Recall']}")
        
        if good_models:
            print(f"   ✅ Good (80-89% HV Recall): {len(good_models)} models")
            for model in good_models:
                print(f"     - {model['Model']}: {model['HV_Recall']}")
        
        if fair_models:
            print(f"   👍 Fair (70-79% HV Recall): {len(fair_models)} models")
            for model in fair_models:
                print(f"     - {model['Model']}: {model['HV_Recall']}")

class FootDataset(Dataset):
    """Dataset class for foot images"""
    
    def __init__(self, dataframe, transform=None):
        self.dataframe = dataframe
        self.transform = transform
        
        # Pre-validate images
        self.valid_indices = []
        for i, (_, row) in enumerate(dataframe.iterrows()):
            image_path = Path("processed_dataset") / row['class'] / row['filename']
            if image_path.exists():
                self.valid_indices.append(i)
    
    def __len__(self):
        return len(self.valid_indices)
    
    def __getitem__(self, idx):
        actual_idx = self.valid_indices[idx]
        row = self.dataframe.iloc[actual_idx]
        
        image_path = Path("processed_dataset") / row['class'] / row['filename']
        
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception:
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        if self.transform:
            image = self.transform(image)
        
        return image, torch.tensor(row['class_id'], dtype=torch.long)

def main():
    """Main function to check all model accuracies"""
    
    try:
        checker = ModelAccuracyChecker()
        checker.check_all_models()
        
        print(f"\n🎉 MODEL ACCURACY CHECK COMPLETED!")
        print(f"   📊 All available models evaluated")
        print(f"   🏆 Best model identified")
        print(f"   💾 Results saved")
        
    except Exception as e:
        print(f"❌ Model accuracy check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
