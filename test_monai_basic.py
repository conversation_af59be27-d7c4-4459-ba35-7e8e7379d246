
"""
Simple MONAI Test for Foot Deformity Classification
Test MONAI installation and basic functionality
"""

def test_monai_basic():
    """Test basic MONAI functionality"""

    print("🏥 TESTING MONAI BASIC FUNCTIONALITY")
    print("=" * 40)

    try:
        import monai
        print(f"✅ MONAI version: {monai.__version__}")

        # Test basic imports
        from monai.networks.nets import DenseNet121
        print("✅ DenseNet121 import successful")

        from monai.transforms import Compose, Resize
        print("✅ Transforms import successful")

        # Create a simple model
        model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=False  # Avoid download issues
        )
        print("✅ MONAI DenseNet121 model created")

        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 Model parameters: {total_params:,}")

        print(f"\n🎉 MONAI BASIC TEST: SUCCESSFUL!")
        print(f"   Ready for medical-grade AI implementation")

        return True

    except ImportError as e:
        print(f"❌ MONAI import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ MONAI test failed: {e}")
        return False

if __name__ == "__main__":
    test_monai_basic()
