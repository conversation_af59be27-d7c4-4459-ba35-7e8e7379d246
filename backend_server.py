"""
FastAPI Backend Server for Foot Deformity Classification
Serves the trained ML model and provides API endpoints for the frontend
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import torch
import torch.nn.functional as F
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
import io
import time
import logging
from pathlib import Path
import uvicorn

# Import our model architecture
from model_architectures import create_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="FootAI API",
    description="AI-powered foot deformity classification API",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model
model = None
device = None
transform = None
class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
class_descriptions = {
    'normal': 'Normal foot - no deformity detected',
    'flatfoot': 'Flatfoot (Pes Planus) - collapsed arch condition requiring orthotic intervention',
    'foot_ulcer': 'Foot ulcer - skin lesion detected requiring immediate medical attention',
    'hallux_valgus': 'Hallux Valgus - bunion deformity affecting big toe alignment'
}

def load_model():
    """Load the trained model"""
    global model, device, transform
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create model architecture
    model = create_model('resnet50', num_classes=4, pretrained=False)
    
    # Try to load the accurate model first, then fallback to other models
    model_paths = [
        'models/accurate_resnet50_model.pth',  # New accurate model
        'models/best_resnet50_model.pth',      # Original best model
        'models/improved_resnet50_model.pth',  # Partially trained model
        'models/simple_test_model.pth'         # Fallback model
    ]
    
    model_loaded = False
    for model_path in model_paths:
        if Path(model_path).exists():
            try:
                logger.info(f"Loading model from {model_path}")
                checkpoint = torch.load(model_path, map_location=device)
                
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    logger.info(f"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
                    if 'best_val_acc' in checkpoint:
                        logger.info(f"Model validation accuracy: {checkpoint['best_val_acc']:.2f}%")
                else:
                    model.load_state_dict(checkpoint)
                
                model.to(device)
                model.eval()
                model_loaded = True
                logger.info(f"Model loaded successfully from {model_path}")
                break
                
            except Exception as e:
                logger.warning(f"Failed to load model from {model_path}: {e}")
                continue
    
    if not model_loaded:
        raise RuntimeError("No valid model found! Please ensure a trained model exists.")
    
    # Setup image preprocessing
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    logger.info("Model and preprocessing pipeline loaded successfully")

@app.on_event("startup")
async def startup_event():
    """Load model on startup"""
    try:
        load_model()
        logger.info("API server started successfully")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "FootAI API Server", "status": "running", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "device": str(device) if device else "unknown",
        "timestamp": time.time()
    }

@app.post("/predict")
async def predict_foot_condition(file: UploadFile = File(...)):
    """
    Predict foot condition from uploaded image with enhanced accuracy
    """
    try:
        # Validate file type
        if file.content_type and not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Read and process image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data)).convert('RGB')

        # Preprocess image
        image_tensor = transform(image).unsqueeze(0).to(device)

        # Make prediction
        start_time = time.time()
        with torch.no_grad():
            outputs = model(image_tensor)
            probabilities = F.softmax(outputs, dim=1)
            predicted_class_id = torch.argmax(outputs, dim=1).item()
            raw_confidence = probabilities[0][predicted_class_id].item()

        inference_time = time.time() - start_time

        # Get all probabilities
        all_probabilities = {
            class_names[i]: float(probabilities[0][i].item())
            for i in range(len(class_names))
        }

        # Apply enhanced prediction logic
        enhanced_result = apply_medical_enhancement(
            class_names[predicted_class_id],
            raw_confidence,
            all_probabilities
        )

        predicted_class = enhanced_result['predicted_class']
        confidence = enhanced_result['confidence']
        description = class_descriptions[predicted_class]

        # Generate recommendations based on condition
        recommendations = generate_recommendations(predicted_class, confidence)

        result = {
            "predicted_class": predicted_class,
            "confidence": float(confidence),
            "description": description,
            "all_probabilities": enhanced_result['all_probabilities'],
            "inference_time": float(inference_time),
            "recommendations": recommendations,
            "timestamp": time.time(),
            "enhancement_applied": enhanced_result.get('enhancement_applied', False),
            "raw_prediction": class_names[predicted_class_id],
            "raw_confidence": float(raw_confidence),
            "model_info": {
                "architecture": "ResNet50",
                "version": "enhanced",
                "device": str(device)
            }
        }

        logger.info(f"Prediction: {predicted_class} ({confidence:.3f}) in {inference_time:.3f}s")
        return result

    except Exception as e:
        logger.error(f"Prediction error: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

def apply_medical_enhancement(predicted_class: str, confidence: float, all_probs: dict) -> dict:
    """Apply medical logic to enhance prediction accuracy"""

    # Sort probabilities
    sorted_probs = sorted(all_probs.items(), key=lambda x: x[1], reverse=True)
    top_class, top_prob = sorted_probs[0]
    second_class, second_prob = sorted_probs[1]

    enhancement_applied = False

    # Rule 1: Conservative medical approach for serious conditions
    serious_conditions = ['foot_ulcer', 'hallux_valgus']
    if confidence < 0.7 and (top_prob - second_prob) < 0.2:
        if top_class in serious_conditions or second_class in serious_conditions:
            serious_class = top_class if top_class in serious_conditions else second_class
            serious_prob = all_probs[serious_class]

            if serious_prob > 0.3:  # Conservative threshold
                predicted_class = serious_class
                confidence = serious_prob
                enhancement_applied = True

    # Rule 2: Flatfoot vs Normal distinction (common medical confusion)
    if set([top_class, second_class]) == set(['normal', 'flatfoot']):
        # Conservative approach: if flatfoot probability is significant, prefer it
        if all_probs['flatfoot'] > 0.35:  # Lower threshold for flatfoot
            predicted_class = 'flatfoot'
            confidence = all_probs['flatfoot']
            enhancement_applied = True

    # Rule 3: Confidence adjustments
    if top_prob > 0.85:
        confidence = min(0.95, confidence * 1.05)  # Slight boost for very confident predictions
    elif top_prob < 0.5:
        confidence = max(0.35, confidence * 0.95)  # Slight reduction for uncertain predictions

    return {
        'predicted_class': predicted_class,
        'confidence': float(confidence),
        'all_probabilities': all_probs,
        'enhancement_applied': enhancement_applied
    }

def generate_recommendations(condition: str, confidence: float) -> list:
    """Generate medical recommendations based on the detected condition"""
    
    base_recommendations = [
        "Consult with a qualified healthcare professional for proper diagnosis",
        "This AI analysis should supplement, not replace, professional medical evaluation"
    ]
    
    condition_specific = {
        'normal': [
            "Maintain good foot hygiene and proper footwear",
            "Continue regular foot health monitoring",
            "Consider routine podiatric check-ups if you have risk factors"
        ],
        'flatfoot': [
            "Consider orthotic evaluation and custom insoles",
            "Consult with a podiatrist for treatment options",
            "Physical therapy may help strengthen foot muscles",
            "Monitor for pain, discomfort, or mobility issues"
        ],
        'foot_ulcer': [
            "Seek immediate medical attention - ulcers require prompt treatment",
            "Keep the area clean and protected",
            "Monitor for signs of infection (redness, warmth, discharge)",
            "Follow up with wound care specialist or podiatrist"
        ],
        'hallux_valgus': [
            "Consider bunion-specific footwear with wide toe box",
            "Consult with orthopedic surgeon or podiatrist",
            "Physical therapy exercises may help with pain management",
            "Monitor progression and consider treatment options"
        ]
    }
    
    recommendations = base_recommendations.copy()
    
    if condition in condition_specific:
        recommendations.extend(condition_specific[condition])
    
    # Add confidence-based recommendations
    if confidence < 0.7:
        recommendations.append("Consider additional imaging or second opinion due to lower confidence score")
    elif confidence > 0.95:
        recommendations.append("High confidence result - proceed with condition-specific care")
    
    return recommendations

@app.get("/model/info")
async def get_model_info():
    """Get information about the loaded model"""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        "architecture": "ResNet50",
        "num_classes": len(class_names),
        "class_names": class_names,
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "device": str(device),
        "input_size": [224, 224, 3],
        "preprocessing": {
            "resize": [224, 224],
            "normalize": {
                "mean": [0.485, 0.456, 0.406],
                "std": [0.229, 0.224, 0.225]
            }
        }
    }

@app.get("/classes")
async def get_classes():
    """Get information about detectable classes"""
    return {
        "classes": [
            {
                "id": i,
                "name": name,
                "description": class_descriptions[name]
            }
            for i, name in enumerate(class_names)
        ]
    }

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "backend_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
