<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FootAI - Demo Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">FootAI</h1>
                        <p class="text-sm text-gray-500">Medical AI Demo</p>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    Backend: <span id="backend-status" class="text-red-500">Checking...</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-2 gap-8">
            <!-- Upload Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Upload Foot Image
                </h2>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer" id="upload-area">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="text-lg font-medium text-gray-700 mb-2">Drag & drop an image here</p>
                    <p class="text-gray-500 mb-4">or click to select a file</p>
                    <input type="file" id="file-input" accept="image/*" class="hidden">
                    <button onclick="document.getElementById('file-input').click()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        Select Image
                    </button>
                </div>

                <div id="image-preview" class="mt-4 hidden">
                    <img id="preview-img" class="w-full h-64 object-contain bg-gray-100 rounded-lg">
                    <div class="mt-4 flex space-x-4">
                        <button onclick="analyzeImage()" id="analyze-btn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Analyze Image
                        </button>
                        <button onclick="resetUpload()" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            Reset
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Analysis Results
                </h2>

                <div id="results-placeholder" class="text-center text-gray-500 py-12">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p>Upload an image to see analysis results</p>
                </div>

                <div id="results-content" class="hidden">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 id="condition-name" class="text-xl font-semibold text-gray-900 capitalize"></h3>
                                <p id="condition-description" class="text-gray-600 mt-1"></p>
                            </div>
                            <div class="text-right">
                                <div id="confidence-score" class="text-3xl font-bold text-blue-600"></div>
                                <div class="text-sm text-gray-500">Confidence</div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h4 class="font-semibold text-gray-900">Probability Breakdown:</h4>
                        <div id="probability-bars"></div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-start space-x-3">
                            <svg class="w-6 h-6 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h5 class="font-semibold text-yellow-800 mb-2">Medical Disclaimer</h5>
                                <p class="text-yellow-700 text-sm">
                                    This AI analysis is for informational purposes only and should not replace professional medical diagnosis or treatment. 
                                    Always consult with qualified healthcare professionals for proper medical evaluation.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">System Status</h2>
            <div class="grid md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">96.9%</div>
                    <div class="text-gray-600">Model Accuracy</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">&lt;50ms</div>
                    <div class="text-gray-600">Processing Time</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">4</div>
                    <div class="text-gray-600">Conditions</div>
                </div>
                <div class="text-center">
                    <div id="status-indicator" class="text-2xl font-bold text-green-600">Online</div>
                    <div class="text-gray-600">API Status</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedFile = null;

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                document.getElementById('backend-status').textContent = 'Online';
                document.getElementById('backend-status').className = 'text-green-500';
                document.getElementById('status-indicator').textContent = 'Online';
                document.getElementById('status-indicator').className = 'text-2xl font-bold text-green-600';
            } catch (error) {
                document.getElementById('backend-status').textContent = 'Offline';
                document.getElementById('backend-status').className = 'text-red-500';
                document.getElementById('status-indicator').textContent = 'Offline';
                document.getElementById('status-indicator').className = 'text-2xl font-bold text-red-600';
            }
        }

        // File input handling
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-img').src = e.target.result;
                    document.getElementById('image-preview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });

        // Analyze image
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }

            const analyzeBtn = document.getElementById('analyze-btn');
            analyzeBtn.innerHTML = '<div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>Analyzing...';
            analyzeBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch('http://localhost:8000/predict', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Analysis failed');
                }

                const result = await response.json();
                displayResults(result);
            } catch (error) {
                alert('Analysis failed: ' + error.message);
            } finally {
                analyzeBtn.innerHTML = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>Analyze Image';
                analyzeBtn.disabled = false;
            }
        }

        // Display results
        function displayResults(result) {
            document.getElementById('results-placeholder').classList.add('hidden');
            document.getElementById('results-content').classList.remove('hidden');

            document.getElementById('condition-name').textContent = result.predicted_class.replace('_', ' ');
            document.getElementById('condition-description').textContent = result.description;
            document.getElementById('confidence-score').textContent = (result.confidence * 100).toFixed(1) + '%';

            // Create probability bars
            const probabilityBars = document.getElementById('probability-bars');
            probabilityBars.innerHTML = '';

            const colors = {
                'normal': '#22c55e',
                'flatfoot': '#3b82f6',
                'foot_ulcer': '#ef4444',
                'hallux_valgus': '#8b5cf6'
            };

            Object.entries(result.all_probabilities)
                .sort(([,a], [,b]) => b - a)
                .forEach(([condition, probability]) => {
                    const bar = document.createElement('div');
                    bar.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
                    bar.innerHTML = `
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full" style="background-color: ${colors[condition] || '#6b7280'}"></div>
                            <span class="font-medium capitalize">${condition.replace('_', ' ')}</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-32 bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full" style="width: ${probability * 100}%; background-color: ${colors[condition] || '#6b7280'}"></div>
                            </div>
                            <span class="font-semibold text-gray-900 w-12 text-right">${(probability * 100).toFixed(1)}%</span>
                        </div>
                    `;
                    probabilityBars.appendChild(bar);
                });
        }

        // Reset upload
        function resetUpload() {
            selectedFile = null;
            document.getElementById('file-input').value = '';
            document.getElementById('image-preview').classList.add('hidden');
            document.getElementById('results-placeholder').classList.remove('hidden');
            document.getElementById('results-content').classList.add('hidden');
        }

        // Initialize
        checkBackendStatus();
        setInterval(checkBackendStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>
