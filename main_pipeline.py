"""
Complete Foot Deformity Classification Pipeline
Main script to run the entire ML pipeline from data preprocessing to model evaluation
"""

import argparse
import torch
import sys
from pathlib import Path
import json

# Import our modules
from dataset_analyzer import FootDeformityDatasetAnalyzer
from data_preprocessing import DataPreprocessor
from training_pipeline import <PERSON><PERSON>eformityTrainer
from evaluation_metrics import ModelEvaluator
from inference_pipeline import FootDeformityPredictor

def run_data_analysis(args):
    """Run dataset analysis and preprocessing"""
    
    print("🔍 STEP 1: Dataset Analysis and Preprocessing")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = FootDeformityDatasetAnalyzer(args.dataset_path)
    
    # Run analysis
    analyzer.analyze_dataset_structure()
    analyzer.create_class_mapping()
    analyzer.generate_dataset_report()
    
    # Create unified dataset if requested
    if args.create_unified:
        output_dir, manifest = analyzer.create_unified_dataset_structure(args.processed_dataset_path)
        print(f"✅ Unified dataset created at: {output_dir}")
    
    return True

def run_training(args):
    """Run model training"""
    
    print("\n🚀 STEP 2: Model Training")
    print("=" * 60)
    
    # Setup data preprocessing
    preprocessor = DataPreprocessor(args.processed_dataset_path)
    preprocessor.load_manifest()
    class_counts, class_weights = preprocessor.analyze_class_distribution()
    
    # Create data splits and loaders
    train_df, val_df, test_df = preprocessor.create_data_splits(
        test_size=args.test_size,
        val_size=args.val_size,
        random_state=args.random_seed
    )
    
    train_loader, val_loader, test_loader = preprocessor.create_data_loaders(
        train_df, val_df, test_df, 
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # Initialize trainer
    trainer = FootDeformityTrainer(
        model_type=args.model_type,
        num_classes=4,
        device=torch.device(args.device)
    )
    
    # Setup model and training
    trainer.setup_model(pretrained=args.pretrained)
    trainer.setup_training(
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        class_weights=class_weights if args.use_class_weights else None
    )
    
    # Train model
    history = trainer.train(
        train_loader, val_loader,
        num_epochs=args.num_epochs,
        save_dir=args.model_save_dir
    )
    
    # Plot training history
    trainer.plot_training_history(f'{args.model_save_dir}/training_history.png')
    
    # Save test loader for evaluation
    torch.save(test_loader, f'{args.model_save_dir}/test_loader.pth')
    
    print(f"✅ Training completed! Best validation accuracy: {trainer.best_val_acc:.2f}%")
    
    return trainer, test_loader

def run_evaluation(args, test_loader=None):
    """Run model evaluation"""
    
    print("\n📊 STEP 3: Model Evaluation")
    print("=" * 60)
    
    # Load test loader if not provided
    if test_loader is None:
        test_loader_path = Path(args.model_save_dir) / 'test_loader.pth'
        if test_loader_path.exists():
            test_loader = torch.load(test_loader_path)
        else:
            print("❌ Test loader not found. Please run training first.")
            return None
    
    # Load trained model
    model_path = Path(args.model_save_dir) / f'best_{args.model_type}_model.pth'
    if not model_path.exists():
        print(f"❌ Trained model not found at {model_path}")
        return None
    
    # Create predictor (which loads the model)
    predictor = FootDeformityPredictor(
        model_path=model_path,
        model_type=args.model_type,
        device=torch.device(args.device)
    )
    
    # Create evaluator
    evaluator = ModelEvaluator(
        model=predictor.model,
        device=torch.device(args.device)
    )
    
    # Run evaluation
    accuracy = evaluator.evaluate_model(test_loader)
    
    # Generate comprehensive evaluation
    evaluation_dir = Path(args.model_save_dir) / 'evaluation_results'
    evaluator.save_evaluation_results(evaluation_dir)
    
    print(f"✅ Evaluation completed! Test accuracy: {accuracy:.2f}%")
    print(f"📁 Detailed results saved to: {evaluation_dir}")
    
    return evaluator

def run_inference_demo(args):
    """Run inference demonstration"""
    
    print("\n🔮 STEP 4: Inference Demonstration")
    print("=" * 60)
    
    # Load trained model
    model_path = Path(args.model_save_dir) / f'best_{args.model_type}_model.pth'
    if not model_path.exists():
        print(f"❌ Trained model not found at {model_path}")
        return None
    
    # Create predictor
    predictor = FootDeformityPredictor(
        model_path=model_path,
        model_type=args.model_type,
        device=torch.device(args.device)
    )
    
    # Demo with sample images from processed dataset
    processed_dataset = Path(args.processed_dataset_path)
    
    print("🖼️  Running inference on sample images...")
    
    # Find sample images from each class
    for class_name in ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']:
        class_dir = processed_dataset / class_name
        if class_dir.exists():
            sample_images = list(class_dir.glob('*.jpg'))[:2]  # Take 2 samples
            
            for img_path in sample_images:
                print(f"\n📸 Analyzing: {img_path.name}")
                result = predictor.predict_single(img_path)
                print(f"   Prediction: {result['predicted_class']} ({result['confidence']:.1%})")
                print(f"   Description: {result['description']}")
    
    print("\n✅ Inference demonstration completed!")
    
    return predictor

def main():
    """Main pipeline execution"""
    
    parser = argparse.ArgumentParser(description='Foot Deformity Classification Pipeline')
    
    # Data paths
    parser.add_argument('--dataset_path', type=str, default='Dataset',
                       help='Path to original dataset')
    parser.add_argument('--processed_dataset_path', type=str, default='processed_dataset',
                       help='Path to processed dataset')
    parser.add_argument('--model_save_dir', type=str, default='models',
                       help='Directory to save trained models')
    
    # Pipeline steps
    parser.add_argument('--skip_analysis', action='store_true',
                       help='Skip dataset analysis step')
    parser.add_argument('--skip_training', action='store_true',
                       help='Skip training step')
    parser.add_argument('--skip_evaluation', action='store_true',
                       help='Skip evaluation step')
    parser.add_argument('--skip_inference', action='store_true',
                       help='Skip inference demo step')
    
    # Data preprocessing
    parser.add_argument('--create_unified', action='store_true', default=True,
                       help='Create unified dataset structure')
    parser.add_argument('--test_size', type=float, default=0.2,
                       help='Test set size (0.0-1.0)')
    parser.add_argument('--val_size', type=float, default=0.1,
                       help='Validation set size (0.0-1.0)')
    parser.add_argument('--random_seed', type=int, default=42,
                       help='Random seed for reproducibility')
    
    # Model configuration
    parser.add_argument('--model_type', type=str, default='resnet50',
                       choices=['resnet50', 'efficientnet_b0', 'densenet121', 'vgg16', 'attention'],
                       help='Model architecture to use')
    parser.add_argument('--pretrained', action='store_true', default=True,
                       help='Use pretrained weights')
    
    # Training parameters
    parser.add_argument('--num_epochs', type=int, default=30,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='Batch size for training')
    parser.add_argument('--learning_rate', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                       help='Weight decay for regularization')
    parser.add_argument('--use_class_weights', action='store_true', default=True,
                       help='Use class weights for balanced training')
    
    # System configuration
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (cuda/cpu/auto)')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of data loader workers')
    
    args = parser.parse_args()
    
    # Auto-detect device
    if args.device == 'auto':
        args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print("🦶 Foot Deformity Classification Pipeline")
    print("=" * 60)
    print(f"📊 Configuration:")
    print(f"   Dataset: {args.dataset_path}")
    print(f"   Model: {args.model_type}")
    print(f"   Device: {args.device}")
    print(f"   Epochs: {args.num_epochs}")
    print(f"   Batch Size: {args.batch_size}")
    print("=" * 60)
    
    # Create directories
    Path(args.model_save_dir).mkdir(exist_ok=True)
    
    # Step 1: Dataset Analysis
    if not args.skip_analysis:
        run_data_analysis(args)
    
    # Step 2: Training
    trainer, test_loader = None, None
    if not args.skip_training:
        trainer, test_loader = run_training(args)
    
    # Step 3: Evaluation
    if not args.skip_evaluation:
        evaluator = run_evaluation(args, test_loader)
    
    # Step 4: Inference Demo
    if not args.skip_inference:
        predictor = run_inference_demo(args)
    
    print("\n🎉 Pipeline completed successfully!")
    print(f"📁 Results saved in: {args.model_save_dir}")
    print("\n🚀 Next steps:")
    print("   1. Review evaluation results")
    print("   2. Test inference on new images")
    print("   3. Deploy model for production use")

if __name__ == "__main__":
    main()
