"""
Quick Demo of the Foot Deformity Classification System
Shows dataset analysis and model architecture testing
"""

import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np

def demo_dataset_analysis():
    """Demonstrate dataset analysis results"""
    
    print("🔍 DATASET ANALYSIS DEMO")
    print("=" * 50)
    
    # Load the dataset manifest
    manifest_path = Path("processed_dataset/dataset_manifest.csv")
    if manifest_path.exists():
        df = pd.read_csv(manifest_path)
        
        print(f"📊 Dataset Summary:")
        print(f"   Total Images: {len(df):,}")
        print(f"   Classes: {df['class'].nunique()}")
        
        # Class distribution
        class_counts = df['class'].value_counts()
        print(f"\n📈 Class Distribution:")
        for class_name, count in class_counts.items():
            percentage = (count / len(df)) * 100
            print(f"   {class_name:15}: {count:4d} images ({percentage:5.1f}%)")
        
        # Create visualization
        plt.figure(figsize=(12, 5))
        
        # Class distribution pie chart
        plt.subplot(1, 2, 1)
        colors = ['lightblue', 'lightcoral', 'lightgreen', 'lightyellow']
        plt.pie(class_counts.values, labels=class_counts.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        plt.title('Class Distribution')
        
        # Class distribution bar chart
        plt.subplot(1, 2, 2)
        bars = plt.bar(class_counts.index, class_counts.values, color=colors)
        plt.title('Images per Class')
        plt.xlabel('Class')
        plt.ylabel('Number of Images')
        plt.xticks(rotation=45)
        
        # Add value labels on bars
        for bar, count in zip(bars, class_counts.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 50,
                    str(count), ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('dataset_distribution.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"\n📊 Dataset visualization saved as 'dataset_distribution.png'")
        
        return df
    else:
        print("❌ Dataset manifest not found. Please run dataset analysis first.")
        return None

def demo_model_architectures():
    """Demonstrate different model architectures"""
    
    print("\n🧠 MODEL ARCHITECTURE DEMO")
    print("=" * 50)
    
    try:
        from model_architectures import create_model, count_parameters
        
        models_to_test = [
            ('resnet50', 'ResNet-50'),
            ('efficientnet_b0', 'EfficientNet-B0'),
            ('densenet121', 'DenseNet-121')
        ]
        
        model_info = []
        dummy_input = torch.randn(1, 3, 224, 224)
        
        for model_name, display_name in models_to_test:
            print(f"\n🔧 Testing {display_name}...")
            
            try:
                # Create model
                model = create_model(model_name, num_classes=4, pretrained=False)
                
                # Count parameters
                total_params, trainable_params = count_parameters(model)
                
                # Test forward pass
                model.eval()
                with torch.no_grad():
                    output = model(dummy_input)
                    output_shape = output.shape
                
                model_info.append({
                    'Model': display_name,
                    'Total Parameters': f"{total_params:,}",
                    'Trainable Parameters': f"{trainable_params:,}",
                    'Output Shape': str(output_shape),
                    'Status': '✅ Working'
                })
                
                print(f"   ✅ {display_name}: {total_params:,} parameters")
                
            except Exception as e:
                model_info.append({
                    'Model': display_name,
                    'Total Parameters': 'Error',
                    'Trainable Parameters': 'Error',
                    'Output Shape': 'Error',
                    'Status': f'❌ {str(e)[:50]}...'
                })
                print(f"   ❌ {display_name}: Error - {e}")
        
        # Create comparison table
        df_models = pd.DataFrame(model_info)
        print(f"\n📊 Model Comparison:")
        print(df_models.to_string(index=False))
        
        return df_models
        
    except ImportError as e:
        print(f"❌ Cannot import model architectures: {e}")
        return None

def demo_training_progress():
    """Show training progress if available"""
    
    print("\n📈 TRAINING PROGRESS DEMO")
    print("=" * 50)
    
    # Check if training is in progress
    models_dir = Path("models")
    if models_dir.exists():
        model_files = list(models_dir.glob("*.pth"))
        if model_files:
            print(f"📁 Found {len(model_files)} model files:")
            for model_file in model_files:
                print(f"   📄 {model_file.name}")
        else:
            print("📝 No trained models found yet. Training may be in progress...")
    else:
        print("📝 Models directory not found. Training not started yet.")
    
    # Check processed dataset
    processed_dir = Path("processed_dataset")
    if processed_dir.exists():
        class_dirs = [d for d in processed_dir.iterdir() if d.is_dir()]
        print(f"\n📁 Processed dataset structure:")
        for class_dir in class_dirs:
            image_count = len(list(class_dir.glob("*.jpg")))
            print(f"   📂 {class_dir.name}: {image_count} images")

def demo_system_info():
    """Show system information"""
    
    print("\n💻 SYSTEM INFORMATION")
    print("=" * 50)
    
    # PyTorch info
    print(f"🔥 PyTorch Version: {torch.__version__}")
    print(f"🖥️  CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"🎮 GPU Device: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("🖥️  Using CPU for training")
    
    # Check available memory
    import psutil
    memory = psutil.virtual_memory()
    print(f"💾 System RAM: {memory.total / 1e9:.1f} GB")
    print(f"💾 Available RAM: {memory.available / 1e9:.1f} GB")
    
    # Recommended settings
    print(f"\n💡 Recommended Settings:")
    if torch.cuda.is_available():
        print(f"   🚀 Use GPU training for faster results")
        print(f"   📊 Batch size: 32-64")
        print(f"   ⏱️  Expected training time: 10-20 minutes")
    else:
        print(f"   🐌 CPU training will be slower")
        print(f"   📊 Batch size: 8-16")
        print(f"   ⏱️  Expected training time: 1-3 hours")

def main():
    """Run the complete demo"""
    
    print("🦶 FOOT DEFORMITY CLASSIFICATION SYSTEM DEMO")
    print("=" * 60)
    
    # System information
    demo_system_info()
    
    # Dataset analysis
    df = demo_dataset_analysis()
    
    # Model architectures
    model_df = demo_model_architectures()
    
    # Training progress
    demo_training_progress()
    
    print(f"\n🎯 DEMO SUMMARY")
    print("=" * 50)
    print("✅ Dataset analysis completed")
    print("✅ Model architectures tested")
    print("✅ System information displayed")
    
    if df is not None:
        print(f"📊 Dataset: {len(df):,} images across {df['class'].nunique()} classes")
    
    print(f"\n🚀 Next Steps:")
    print(f"   1. Wait for training to complete")
    print(f"   2. Review model performance")
    print(f"   3. Test inference on new images")
    print(f"   4. Deploy for production use")

if __name__ == "__main__":
    main()
