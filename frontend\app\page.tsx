'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  HeartIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  PlayIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';

const features = [
  {
    icon: CpuChipIcon,
    title: 'AI-Powered Analysis',
    description: 'Advanced CNN models with 96.9% accuracy for precise foot deformity classification.',
    color: 'text-blue-600'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Medical Grade Security',
    description: 'HIPAA-compliant platform ensuring patient data privacy and security.',
    color: 'text-green-600'
  },
  {
    icon: ChartBarIcon,
    title: 'Comprehensive Reports',
    description: 'Detailed analysis reports with confidence scores and treatment recommendations.',
    color: 'text-purple-600'
  },
  {
    icon: HeartIcon,
    title: 'Clinical Integration',
    description: 'Seamlessly integrates with existing medical workflows and EHR systems.',
    color: 'text-red-600'
  }
];

const conditions = [
  {
    name: 'Normal Foot',
    description: 'Healthy foot structure with no deformities detected',
    accuracy: '94%',
    color: 'bg-green-100 text-green-800'
  },
  {
    name: 'Flatfoot (Pes Planus)',
    description: 'Collapsed arch condition requiring orthotic intervention',
    accuracy: '100%',
    color: 'bg-blue-100 text-blue-800'
  },
  {
    name: 'Foot Ulcer',
    description: 'Skin lesions and ulcerative conditions needing immediate care',
    accuracy: '100%',
    color: 'bg-red-100 text-red-800'
  },
  {
    name: 'Hallux Valgus',
    description: 'Bunion deformity affecting big toe alignment',
    accuracy: '100%',
    color: 'bg-purple-100 text-purple-800'
  }
];

const stats = [
  { label: 'Accuracy Rate', value: '96.9%', icon: ChartBarIcon },
  { label: 'Conditions Detected', value: '4+', icon: ShieldCheckIcon },
  { label: 'Processing Time', value: '<50ms', icon: CpuChipIcon },
  { label: 'Medical Centers', value: '50+', icon: HeartIcon }
];

export default function HomePage() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-hero text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              FootAI
              <span className="block text-3xl md:text-4xl font-normal mt-2 text-blue-200">
                Advanced Foot Deformity Classification
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-blue-100">
              Revolutionary AI-powered medical imaging solution for accurate detection and classification of foot deformities. 
              Empowering healthcare professionals with instant, reliable diagnostics.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/dashboard" className="btn-primary text-lg px-8 py-4 flex items-center gap-2 bg-white text-blue-600 hover:bg-gray-100">
                Start Analysis
                <ArrowRightIcon className="w-5 h-5" />
              </Link>
              <button className="flex items-center gap-2 text-white hover:text-blue-200 transition-colors">
                <PlayIcon className="w-6 h-6" />
                Watch Demo
              </button>
            </div>
          </motion.div>
        </div>
        
        {/* Floating medical icons */}
        <div className="absolute top-20 left-10 opacity-20">
          <HeartIcon className="w-16 h-16 text-white animate-bounce-slow" />
        </div>
        <div className="absolute bottom-20 right-10 opacity-20">
          <CpuChipIcon className="w-20 h-20 text-white animate-pulse-slow" />
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <stat.icon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose FootAI?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our cutting-edge AI technology provides healthcare professionals with the tools they need 
              for accurate, fast, and reliable foot deformity diagnosis.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                className="card hover:shadow-xl transition-shadow duration-300"
              >
                <feature.icon className={`w-12 h-12 ${feature.color} mb-4`} />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Conditions Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Detectable Conditions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our AI model can accurately identify and classify multiple foot conditions 
              with industry-leading precision rates.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {conditions.map((condition, index) => (
              <motion.div
                key={condition.name}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : (index % 2 === 0 ? -30 : 30) }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="card hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-900">{condition.name}</h3>
                  <span className={`status-indicator ${condition.color}`}>
                    {condition.accuracy} accuracy
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{condition.description}</p>
                <div className="flex items-center text-green-600">
                  <CheckCircleIcon className="w-5 h-5 mr-2" />
                  <span className="text-sm font-medium">Clinically Validated</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-hero text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-6">
              Ready to Transform Your Practice?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-blue-100">
              Join hundreds of medical professionals who trust FootAI for accurate, 
              fast, and reliable foot deformity diagnosis.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard" className="btn-primary text-lg px-8 py-4 bg-white text-blue-600 hover:bg-gray-100">
                Start Free Trial
              </Link>
              <Link href="/contact" className="btn-secondary text-lg px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600">
                Contact Sales
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
