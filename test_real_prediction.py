"""
Test real predictions with actual foot images from the dataset
"""

import torch
import torch.nn.functional as F
from PIL import Image
import torchvision.transforms as transforms
import requests
import json
from pathlib import Path
import random

# Import our modules
from model_architectures import create_model

def test_model_directly():
    """Test the model directly (not through API)"""
    
    print("🧪 Testing Model Directly")
    print("=" * 40)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = create_model('resnet50', num_classes=4, pretrained=False)
    
    # Load improved model
    model_path = 'models/improved_resnet50_model.pth'
    if Path(model_path).exists():
        print(f"📁 Loading model from {model_path}")
        checkpoint = torch.load(model_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
            if 'best_val_acc' in checkpoint:
                print(f"📊 Model validation accuracy: {checkpoint['best_val_acc']:.2f}%")
        else:
            model.load_state_dict(checkpoint)
        
        model.to(device)
        model.eval()
        print(f"✅ Model loaded successfully on {device}")
    else:
        print(f"❌ Model file not found: {model_path}")
        return False
    
    # Setup preprocessing
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
    
    # Test with sample images from dataset
    processed_dataset = Path("processed_dataset")
    if not processed_dataset.exists():
        print("❌ Processed dataset not found!")
        return False
    
    print(f"\n🔍 Testing with real images from dataset:")
    
    for class_name in class_names:
        class_dir = processed_dataset / class_name
        if class_dir.exists():
            image_files = list(class_dir.glob("*.jpg"))
            if image_files:
                # Test with first image from each class
                test_image = image_files[0]
                
                try:
                    # Load and preprocess image
                    image = Image.open(test_image).convert('RGB')
                    image_tensor = transform(image).unsqueeze(0).to(device)
                    
                    # Make prediction
                    with torch.no_grad():
                        outputs = model(image_tensor)
                        probabilities = F.softmax(outputs, dim=1)
                        predicted_class_id = torch.argmax(outputs, dim=1).item()
                        confidence = probabilities[0][predicted_class_id].item()
                    
                    predicted_class = class_names[predicted_class_id]
                    correct = predicted_class == class_name
                    
                    print(f"\n📸 {class_name} class:")
                    print(f"   Image: {test_image.name}")
                    print(f"   True: {class_name}")
                    print(f"   Predicted: {predicted_class}")
                    print(f"   Confidence: {confidence:.1%}")
                    print(f"   Correct: {'✅' if correct else '❌'}")
                    
                    # Show all probabilities
                    print(f"   All probabilities:")
                    for i, prob in enumerate(probabilities[0]):
                        print(f"     {class_names[i]:15}: {prob.item():.1%}")
                    
                except Exception as e:
                    print(f"   ❌ Error processing {test_image.name}: {e}")
    
    return True

def test_api_prediction():
    """Test prediction through the API"""
    
    print(f"\n🌐 Testing API Prediction")
    print("=" * 40)
    
    # Check if backend is running
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code != 200:
            print("❌ Backend not responding")
            return False
        print("✅ Backend is running")
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False
    
    # Test with a real image
    processed_dataset = Path("processed_dataset")
    test_images = []
    
    for class_name in ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']:
        class_dir = processed_dataset / class_name
        if class_dir.exists():
            image_files = list(class_dir.glob("*.jpg"))
            if image_files:
                test_images.append((class_name, image_files[0]))
    
    if not test_images:
        print("❌ No test images found")
        return False
    
    print(f"🔍 Testing API with {len(test_images)} images:")
    
    for true_class, image_path in test_images:
        try:
            print(f"\n📸 Testing {true_class} image: {image_path.name}")
            
            # Send image to API
            with open(image_path, 'rb') as f:
                files = {'file': f}
                response = requests.post('http://localhost:8000/predict', files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                predicted_class = result['predicted_class']
                confidence = result['confidence']
                correct = predicted_class == true_class
                
                print(f"   True: {true_class}")
                print(f"   Predicted: {predicted_class}")
                print(f"   Confidence: {confidence:.1%}")
                print(f"   Correct: {'✅' if correct else '❌'}")
                print(f"   Processing time: {result['inference_time']*1000:.1f}ms")
                
                # Show top 2 predictions
                sorted_probs = sorted(result['all_probabilities'].items(), key=lambda x: x[1], reverse=True)
                print(f"   Top predictions:")
                for i, (class_name, prob) in enumerate(sorted_probs[:2]):
                    print(f"     {i+1}. {class_name}: {prob:.1%}")
                
            else:
                print(f"   ❌ API error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return True

def main():
    """Run comprehensive prediction tests"""
    
    print("🦶 FOOTAI PREDICTION ACCURACY TEST")
    print("=" * 60)
    
    # Test 1: Direct model testing
    direct_success = test_model_directly()
    
    # Test 2: API testing
    api_success = test_api_prediction()
    
    print(f"\n🏆 TEST SUMMARY")
    print("=" * 60)
    print(f"Direct Model Test: {'✅ PASSED' if direct_success else '❌ FAILED'}")
    print(f"API Prediction Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    
    if direct_success and api_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Your FootAI system is working correctly with real predictions!")
    else:
        print(f"\n⚠️  Some tests failed. Check the errors above.")
        print(f"💡 Make sure:")
        print(f"   • The improved model is properly trained")
        print(f"   • The backend server is running")
        print(f"   • The processed dataset exists")

if __name__ == "__main__":
    main()
