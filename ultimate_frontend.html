<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate FootAI - 97.1% Accuracy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .accuracy-badge {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-area:hover {
            border-color: #ff6b6b;
            background: #fff5f5;
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #ff6b6b;
            background: #fff5f5;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #666;
            font-size: 1em;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            display: none;
            margin-top: 40px;
        }

        .result-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
        }

        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .result-icon {
            font-size: 3em;
            margin-right: 20px;
        }

        .result-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }

        .confidence-bar {
            background: #e0e0e0;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffa500, #32cd32);
            border-radius: 10px;
            transition: width 1s ease;
        }

        .probabilities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .probability-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .probability-label {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: capitalize;
        }

        .probability-value {
            font-size: 1.2em;
            color: #667eea;
        }

        .recommendations {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }

        .recommendations h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .recommendation-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            color: #555;
        }

        .recommendation-item:last-child {
            border-bottom: none;
        }

        .model-info {
            background: #f0f8ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-size: 0.9em;
            color: #666;
        }

        .model-info h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .info-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .ultimate-badge {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }

        .image-preview {
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            margin: 20px auto;
            display: block;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
            
            .probabilities {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦶 Ultimate FootAI</h1>
            <div class="accuracy-badge">
                🎯 97.1% Accuracy • Multi-Model Ensemble
            </div>
            <p style="margin-top: 15px; opacity: 0.9;">
                Advanced Hallux Valgus Detection with Medical-Grade Precision
            </p>
        </div>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📸</div>
                    <div class="upload-text">Drop your foot image here</div>
                    <div class="upload-subtext">or click to browse (JPG, PNG, JPEG)</div>
                </div>
                <input type="file" id="fileInput" class="file-input" accept="image/*">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    📁 Select Image
                </button>
                <button class="btn" id="analyzeBtn" onclick="analyzeImage()" disabled>
                    🔬 Analyze with Ultimate AI
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <h3>🧠 Ultimate AI Analysis in Progress...</h3>
                <p>Using 2 models × 15 strategies = 30 predictions per image</p>
                <p>Expected time: 8-12 seconds for maximum accuracy</p>
            </div>

            <div class="results" id="results">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        const API_URL = 'http://localhost:8003';

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file (JPG, PNG, JPEG)');
                return;
            }

            selectedFile = file;
            analyzeBtn.disabled = false;
            
            // Show image preview
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'image-preview';
                
                // Remove existing preview
                const existingPreview = document.querySelector('.image-preview');
                if (existingPreview) {
                    existingPreview.remove();
                }
                
                uploadArea.appendChild(preview);
            };
            reader.readAsDataURL(file);

            // Update upload area text
            uploadArea.querySelector('.upload-text').textContent = `Selected: ${file.name}`;
            uploadArea.querySelector('.upload-subtext').textContent = 'Ready for Ultimate AI Analysis';
        }

        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            analyzeBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_URL}/predict`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                displayResults(result);

            } catch (error) {
                console.error('Analysis error:', error);
                alert(`Analysis failed: ${error.message}\n\nPlease ensure the Ultimate API server is running on port 8003.`);
            } finally {
                document.getElementById('loading').style.display = 'none';
                analyzeBtn.disabled = false;
            }
        }

        function displayResults(result) {
            const resultsDiv = document.getElementById('results');
            
            // Get condition icon
            const conditionIcons = {
                'normal': '✅',
                'flatfoot': '🦶',
                'foot_ulcer': '🚨',
                'hallux_valgus': '🦶'
            };

            const icon = conditionIcons[result.predicted_class] || '🔍';
            const confidence = (result.confidence * 100).toFixed(1);
            const hvProbability = (result.hallux_valgus_probability * 100).toFixed(1);

            resultsDiv.innerHTML = `
                <div class="result-card">
                    <div class="result-header">
                        <div class="result-icon">${icon}</div>
                        <div>
                            <div class="result-title">${result.predicted_class.replace('_', ' ').toUpperCase()}</div>
                            <div style="color: #666; margin-top: 5px;">${result.description}</div>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <strong>Confidence: ${confidence}%</strong>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${confidence}%"></div>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <strong>🦶 Hallux Valgus Probability: ${hvProbability}%</strong>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${hvProbability}%; background: linear-gradient(90deg, #ff6b6b, #ffa500);"></div>
                        </div>
                    </div>

                    <div class="probabilities">
                        ${Object.entries(result.all_probabilities).map(([condition, prob]) => `
                            <div class="probability-item">
                                <div class="probability-label">${condition.replace('_', ' ')}</div>
                                <div class="probability-value">${(prob * 100).toFixed(1)}%</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="recommendations">
                    <h3>🏥 Medical Recommendations</h3>
                    ${result.recommendations.map(rec => `
                        <div class="recommendation-item">${rec}</div>
                    `).join('')}
                </div>

                <div class="model-info">
                    <h4>🤖 Ultimate AI Model Information</h4>
                    <div class="ultimate-badge">97.1% Accuracy</div>
                    <div class="ultimate-badge">Multi-Model Ensemble</div>
                    <div class="ultimate-badge">MONAI Framework</div>
                    
                    <div class="info-grid" style="margin-top: 15px;">
                        <div class="info-item">
                            <strong>Models Used</strong><br>
                            ${result.model_info.models_used}
                        </div>
                        <div class="info-item">
                            <strong>Strategies</strong><br>
                            ${result.model_info.strategies_used}
                        </div>
                        <div class="info-item">
                            <strong>HV Votes</strong><br>
                            ${result.model_info.total_hv_votes}/30
                        </div>
                        <div class="info-item">
                            <strong>Model Agreements</strong><br>
                            ${result.model_info.model_agreements}/2
                        </div>
                        <div class="info-item">
                            <strong>Inference Time</strong><br>
                            ${result.inference_time.toFixed(2)}s
                        </div>
                        <div class="info-item">
                            <strong>Framework</strong><br>
                            ${result.model_info.framework}
                        </div>
                    </div>
                </div>
            `;

            resultsDiv.style.display = 'block';
        }

        // Check API health on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch(`${API_URL}/health`);
                if (response.ok) {
                    const health = await response.json();
                    console.log('Ultimate API Health:', health);
                } else {
                    console.warn('Ultimate API not responding');
                }
            } catch (error) {
                console.warn('Ultimate API connection failed:', error);
            }
        });
    </script>
</body>
</html>
