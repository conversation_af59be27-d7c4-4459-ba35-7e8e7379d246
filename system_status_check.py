"""
Comprehensive System Status Check
Check all components of the Vision Transformer and MONAI implementation
"""

import torch
import pandas as pd
from pathlib import Path
import json
import time

def check_dataset_status():
    """Check dataset status and characteristics"""
    print("📊 DATASET STATUS CHECK")
    print("-" * 40)
    
    # Check processed dataset
    processed_dir = Path("processed_dataset")
    if processed_dir.exists():
        print("✅ Processed dataset directory exists")
        
        # Check manifest
        manifest_path = processed_dir / "dataset_manifest.csv"
        if manifest_path.exists():
            df = pd.read_csv(manifest_path)
            print(f"✅ Dataset manifest: {len(df):,} images")
            
            # Class distribution
            class_dist = df['class'].value_counts()
            print("📊 Class distribution:")
            for class_name, count in class_dist.items():
                percentage = (count / len(df)) * 100
                print(f"   {class_name:15}: {count:4d} ({percentage:5.1f}%)")
            
            # Check class directories
            for class_name in class_dist.index:
                class_dir = processed_dir / class_name
                if class_dir.exists():
                    image_count = len(list(class_dir.glob("*.jpg")))
                    print(f"✅ {class_name} directory: {image_count} images")
                else:
                    print(f"❌ {class_name} directory missing")
        else:
            print("❌ Dataset manifest missing")
    else:
        print("❌ Processed dataset directory missing")

def check_models_status():
    """Check trained models status"""
    print("\n🤖 MODELS STATUS CHECK")
    print("-" * 40)
    
    models_dir = Path("models")
    if not models_dir.exists():
        print("❌ Models directory missing")
        return
    
    model_files = [
        ("ViT Simple", "vit_simple_best.pth"),
        ("ResNet50 Best", "best_resnet50_model.pth"),
        ("ResNet50 Improved", "improved_resnet50_model.pth"),
        ("MONAI Model", "monai_densenet121_best.pth")
    ]
    
    available_models = []
    
    for model_name, filename in model_files:
        model_path = models_dir / filename
        if model_path.exists():
            try:
                # Load checkpoint info
                checkpoint = torch.load(model_path, map_location='cpu')
                
                size_mb = model_path.stat().st_size / (1024 * 1024)
                val_acc = checkpoint.get('best_val_acc', 0)
                epoch = checkpoint.get('epoch', 'Unknown')
                
                print(f"✅ {model_name:20}: {val_acc:5.1f}% acc, {size_mb:5.1f}MB (Epoch {epoch})")
                available_models.append({
                    'name': model_name,
                    'accuracy': val_acc,
                    'size_mb': size_mb,
                    'epoch': epoch
                })
                
            except Exception as e:
                print(f"❌ {model_name:20}: Error loading - {e}")
        else:
            print(f"❌ {model_name:20}: Not found")
    
    if available_models:
        best_model = max(available_models, key=lambda x: x['accuracy'])
        print(f"\n🏆 Best Model: {best_model['name']} ({best_model['accuracy']:.1f}% accuracy)")
    
    return available_models

def check_backend_status():
    """Check backend API status"""
    print("\n🚀 BACKEND STATUS CHECK")
    print("-" * 40)
    
    backend_files = [
        ("Standard Backend", "backend_server.py"),
        ("ViT Enhanced Backend", "vit_enhanced_backend.py"),
        ("MONAI Backend", "monai_backend.py")
    ]
    
    for backend_name, filename in backend_files:
        if Path(filename).exists():
            print(f"✅ {backend_name:20}: File exists")
        else:
            print(f"❌ {backend_name:20}: File missing")
    
    # Check if any backend is running
    import subprocess
    try:
        # Check for Python processes (backends)
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        if 'python.exe' in result.stdout:
            print("✅ Python processes running (possible backend)")
        else:
            print("❌ No Python processes detected")
    except:
        print("⚠️ Cannot check running processes")

def check_frontend_status():
    """Check frontend and demo status"""
    print("\n🌐 FRONTEND STATUS CHECK")
    print("-" * 40)
    
    frontend_files = [
        ("Simple Demo", "simple_demo.html"),
        ("ViT Interpretability Demo", "vit_interpretability_demo.html"),
        ("Quick Demo", "quick_demo.py"),
        ("Demo Server", "serve_demo.py")
    ]
    
    for frontend_name, filename in frontend_files:
        if Path(filename).exists():
            print(f"✅ {frontend_name:25}: Available")
        else:
            print(f"❌ {frontend_name:25}: Missing")
    
    # Check Next.js frontend
    frontend_dir = Path("frontend")
    if frontend_dir.exists():
        package_json = frontend_dir / "package.json"
        if package_json.exists():
            print("✅ Next.js Frontend: Available")
        else:
            print("❌ Next.js Frontend: Incomplete")
    else:
        print("❌ Next.js Frontend: Missing")

def check_interpretability_status():
    """Check interpretability components"""
    print("\n🔍 INTERPRETABILITY STATUS CHECK")
    print("-" * 40)
    
    interpretability_files = [
        ("Model Interpretability", "model_interpretability.py"),
        ("ViT Backend Integration", "vit_backend_integration.py"),
        ("ViT Model Architecture", "vit_model_architecture.py"),
        ("Interpretability Demo Image", "vit_interpretability_demo.png")
    ]
    
    for component_name, filename in interpretability_files:
        if Path(filename).exists():
            print(f"✅ {component_name:25}: Available")
        else:
            print(f"❌ {component_name:25}: Missing")

def check_monai_status():
    """Check MONAI implementation status"""
    print("\n🏥 MONAI STATUS CHECK")
    print("-" * 40)
    
    # Check MONAI implementation file
    if Path("monai_implementation.py").exists():
        print("✅ MONAI Implementation: Available")
    else:
        print("❌ MONAI Implementation: Missing")
    
    # Check if MONAI is installed
    try:
        import monai
        print(f"✅ MONAI Library: v{monai.__version__} installed")
    except ImportError:
        print("❌ MONAI Library: Not installed")
    
    # Check for MONAI model
    monai_model_path = Path("models/monai_densenet121_best.pth")
    if monai_model_path.exists():
        try:
            checkpoint = torch.load(monai_model_path, map_location='cpu')
            val_acc = checkpoint.get('best_val_acc', 0)
            print(f"✅ MONAI Model: {val_acc:.1f}% accuracy")
        except:
            print("❌ MONAI Model: Error loading")
    else:
        print("❌ MONAI Model: Not trained yet")

def generate_recommendations():
    """Generate recommendations based on status"""
    print("\n💡 RECOMMENDATIONS")
    print("-" * 40)
    
    # Check what's available and recommend next steps
    models_dir = Path("models")
    available_models = []
    
    if models_dir.exists():
        for model_file in models_dir.glob("*.pth"):
            try:
                checkpoint = torch.load(model_file, map_location='cpu')
                val_acc = checkpoint.get('best_val_acc', 0)
                available_models.append((model_file.stem, val_acc))
            except:
                continue
    
    if available_models:
        best_model = max(available_models, key=lambda x: x[1])
        print(f"🏆 Best Model: {best_model[0]} ({best_model[1]:.1f}% accuracy)")
        
        if best_model[1] > 90:
            print("✅ READY FOR PRODUCTION DEPLOYMENT")
            print("   Recommended actions:")
            print("   1. Start backend server")
            print("   2. Test with external images")
            print("   3. Deploy to production")
        elif best_model[1] > 80:
            print("⚠️ GOOD PERFORMANCE - OPTIMIZATION RECOMMENDED")
            print("   Recommended actions:")
            print("   1. Apply advanced data augmentation")
            print("   2. Try ensemble methods")
            print("   3. Implement MONAI for medical optimization")
        else:
            print("❌ NEEDS IMPROVEMENT")
            print("   Recommended actions:")
            print("   1. Collect more training data")
            print("   2. Try different architectures")
            print("   3. Implement MONAI medical framework")
    
    # Check for MONAI
    try:
        import monai
        print("\n🏥 MONAI AVAILABLE - RECOMMENDED NEXT STEP")
        print("   Medical-grade AI framework ready for implementation")
    except ImportError:
        print("\n🏥 MONAI RECOMMENDED")
        print("   Install MONAI for medical-grade AI: pip install monai[all]")

def main():
    """Main status check function"""
    print("🔍 COMPREHENSIVE SYSTEM STATUS CHECK")
    print("=" * 60)
    print("Vision Transformer + MONAI Medical AI System")
    print("=" * 60)
    
    # Run all checks
    check_dataset_status()
    check_models_status()
    check_backend_status()
    check_frontend_status()
    check_interpretability_status()
    check_monai_status()
    generate_recommendations()
    
    print(f"\n🎉 STATUS CHECK COMPLETE")
    print("=" * 60)
    
    # Summary
    print("📋 SYSTEM SUMMARY:")
    
    # Dataset
    if Path("processed_dataset/dataset_manifest.csv").exists():
        df = pd.read_csv("processed_dataset/dataset_manifest.csv")
        print(f"   📊 Dataset: {len(df):,} images across 4 classes")
    
    # Models
    models_count = len(list(Path("models").glob("*.pth"))) if Path("models").exists() else 0
    print(f"   🤖 Models: {models_count} trained models available")
    
    # Implementation
    vit_available = Path("vit_model_architecture.py").exists()
    monai_available = Path("monai_implementation.py").exists()
    print(f"   🧠 ViT: {'✅ Implemented' if vit_available else '❌ Missing'}")
    print(f"   🏥 MONAI: {'✅ Ready' if monai_available else '❌ Missing'}")
    
    # Interpretability
    interpretability_available = Path("model_interpretability.py").exists()
    print(f"   🔍 Interpretability: {'✅ Available' if interpretability_available else '❌ Missing'}")
    
    print(f"\n🚀 READY FOR: Medical-grade AI deployment with interpretability!")

if __name__ == "__main__":
    main()
