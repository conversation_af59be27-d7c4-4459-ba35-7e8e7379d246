{"version": 3, "file": "parse-args.js", "sourceRoot": "", "sources": ["../../src/parse-args.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4B;AAE5B,MAAM,EAAE,GACN,OAAO,OAAO,KAAK,QAAQ;IAC3B,CAAC,CAAC,OAAO;IACT,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ;IACjC,CAAC,CAAC,OAAO,CAAC,OAAO;IACjB,CAAC,CAAC,QAAQ,CAAA;AACd,MAAM,GAAG,GAAG,EAAE;KACX,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;KACjB,KAAK,CAAC,GAAG,CAAC;KACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAE5B,qBAAqB;AACrB,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAA;AAClC,oBAAoB;AAEpB,IAAI,EACF,SAAS,EAAE,EAAE,GACd,GAA8D,IAAI,CAAA;AAEnE,qBAAqB;AACrB,IACE,CAAC,EAAE;IACH,KAAK,GAAG,EAAE;IACV,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;IAC5B,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC,EAC5B;IACA,oBAAoB;IACpB,iDAAiD;IACjD,YAAY;IACZ,EAAE,GAAG,CAAC,MAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAA;CAClD;AAEY,QAAA,SAAS,GAAG,EAAwC,CAAA", "sourcesContent": ["import * as util from 'util'\n\nconst pv =\n  typeof process === 'object' &&\n  !!process &&\n  typeof process.version === 'string'\n    ? process.version\n    : 'v0.0.0'\nconst pvs = pv\n  .replace(/^v/, '')\n  .split('.')\n  .map(s => parseInt(s, 10))\n\n/* c8 ignore start */\nconst [major = 0, minor = 0] = pvs\n/* c8 ignore stop */\n\nlet {\n  parseArgs: pa,\n}: typeof import('util') | typeof import('@pkgjs/parseargs') = util\n\n/* c8 ignore start */\nif (\n  !pa ||\n  major < 16 ||\n  (major === 18 && minor < 11) ||\n  (major === 16 && minor < 19)\n) {\n  /* c8 ignore stop */\n  // Ignore because we will clobber it for commonjs\n  //@ts-ignore\n  pa = (await import('@pkgjs/parseargs')).parseArgs\n}\n\nexport const parseArgs = pa as typeof import('util')['parseArgs']\n"]}