"""
CNN Model Architectures for Foot Deformity Classification
Implements various CNN models with transfer learning
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.models import ResNet50_Weights, EfficientNet_B0_Weights, DenseNet121_Weights
import timm

class FootDeformityCNN(nn.Module):
    """Base CNN model for foot deformity classification"""
    
    def __init__(self, num_classes=4, model_name='resnet50', pretrained=True, dropout_rate=0.5):
        super(FootDeformityCNN, self).__init__()
        
        self.num_classes = num_classes
        self.model_name = model_name
        self.dropout_rate = dropout_rate
        
        # Load backbone model
        self.backbone = self._create_backbone(model_name, pretrained)
        
        # Get feature dimension
        self.feature_dim = self._get_feature_dim()
        
        # Create classifier head
        self.classifier = self._create_classifier_head()
        
    def _create_backbone(self, model_name, pretrained):
        """Create the backbone CNN model"""
        
        if model_name == 'resnet50':
            if pretrained:
                backbone = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
            else:
                backbone = models.resnet50(weights=None)
            # Remove the final classification layer
            backbone = nn.Sequential(*list(backbone.children())[:-1])
            
        elif model_name == 'efficientnet_b0':
            if pretrained:
                backbone = models.efficientnet_b0(weights=EfficientNet_B0_Weights.IMAGENET1K_V1)
            else:
                backbone = models.efficientnet_b0(weights=None)
            # Remove classifier
            backbone.classifier = nn.Identity()
            
        elif model_name == 'densenet121':
            if pretrained:
                backbone = models.densenet121(weights=DenseNet121_Weights.IMAGENET1K_V1)
            else:
                backbone = models.densenet121(weights=None)
            # Remove classifier
            backbone.classifier = nn.Identity()
            
        elif model_name == 'vgg16':
            backbone = models.vgg16(pretrained=pretrained)
            # Remove classifier
            backbone.classifier = nn.Identity()
            
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        
        return backbone
    
    def _get_feature_dim(self):
        """Get the feature dimension of the backbone"""
        
        if self.model_name == 'resnet50':
            return 2048
        elif self.model_name == 'efficientnet_b0':
            return 1280
        elif self.model_name == 'densenet121':
            return 1024
        elif self.model_name == 'vgg16':
            return 25088  # 512 * 7 * 7
        else:
            # Dynamically determine feature dimension
            dummy_input = torch.randn(1, 3, 224, 224)
            with torch.no_grad():
                features = self.backbone(dummy_input)
                return features.view(features.size(0), -1).size(1)
    
    def _create_classifier_head(self):
        """Create the classification head"""
        
        classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)) if self.model_name == 'vgg16' else nn.Identity(),
            nn.Flatten(),
            nn.Dropout(self.dropout_rate),
            nn.Linear(self.feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(self.dropout_rate * 0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(self.dropout_rate * 0.3),
            nn.Linear(256, self.num_classes)
        )
        
        return classifier
    
    def forward(self, x):
        """Forward pass"""
        # Extract features
        features = self.backbone(x)
        
        # Flatten if needed
        if len(features.shape) > 2:
            features = features.view(features.size(0), -1)
        
        # Classify
        output = self.classifier(features)
        
        return output
    
    def freeze_backbone(self):
        """Freeze backbone parameters for transfer learning"""
        for param in self.backbone.parameters():
            param.requires_grad = False
    
    def unfreeze_backbone(self):
        """Unfreeze backbone parameters for fine-tuning"""
        for param in self.backbone.parameters():
            param.requires_grad = True

class EnsembleModel(nn.Module):
    """Ensemble of multiple CNN models"""
    
    def __init__(self, models_config, num_classes=4):
        super(EnsembleModel, self).__init__()
        
        self.models = nn.ModuleList()
        self.num_models = len(models_config)
        
        # Create individual models
        for config in models_config:
            model = FootDeformityCNN(
                num_classes=num_classes,
                model_name=config['name'],
                pretrained=config.get('pretrained', True),
                dropout_rate=config.get('dropout_rate', 0.5)
            )
            self.models.append(model)
        
        # Final ensemble layer
        self.ensemble_classifier = nn.Linear(num_classes * self.num_models, num_classes)
        
    def forward(self, x):
        """Forward pass through ensemble"""
        outputs = []
        
        for model in self.models:
            output = model(x)
            outputs.append(output)
        
        # Concatenate outputs
        ensemble_input = torch.cat(outputs, dim=1)
        
        # Final prediction
        final_output = self.ensemble_classifier(ensemble_input)
        
        return final_output

class AttentionCNN(nn.Module):
    """CNN with attention mechanism for foot deformity classification"""
    
    def __init__(self, num_classes=4, backbone='resnet50', pretrained=True):
        super(AttentionCNN, self).__init__()
        
        # Load backbone
        if backbone == 'resnet50':
            self.backbone = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2 if pretrained else None)
            self.feature_dim = 2048
            # Remove final layers
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-2])
        
        # Attention mechanism
        self.attention = nn.Sequential(
            nn.Conv2d(self.feature_dim, 512, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Global average pooling
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # Classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(self.feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        # Extract features
        features = self.backbone(x)  # [B, C, H, W]
        
        # Generate attention map
        attention_map = self.attention(features)  # [B, 1, H, W]
        
        # Apply attention
        attended_features = features * attention_map  # [B, C, H, W]
        
        # Global pooling
        pooled_features = self.global_pool(attended_features)  # [B, C, 1, 1]
        pooled_features = pooled_features.view(pooled_features.size(0), -1)  # [B, C]
        
        # Classification
        output = self.classifier(pooled_features)
        
        return output

def create_model(model_type='resnet50', num_classes=4, pretrained=True, **kwargs):
    """Factory function to create models"""
    
    if model_type in ['resnet50', 'efficientnet_b0', 'densenet121', 'vgg16']:
        model = FootDeformityCNN(
            num_classes=num_classes,
            model_name=model_type,
            pretrained=pretrained,
            dropout_rate=kwargs.get('dropout_rate', 0.5)
        )
    
    elif model_type == 'ensemble':
        models_config = kwargs.get('models_config', [
            {'name': 'resnet50', 'pretrained': True},
            {'name': 'efficientnet_b0', 'pretrained': True},
            {'name': 'densenet121', 'pretrained': True}
        ])
        model = EnsembleModel(models_config, num_classes)
    
    elif model_type == 'attention':
        model = AttentionCNN(
            num_classes=num_classes,
            backbone=kwargs.get('backbone', 'resnet50'),
            pretrained=pretrained
        )
    
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

def count_parameters(model):
    """Count the number of trainable parameters"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    return total_params, trainable_params

def test_model_architectures():
    """Test different model architectures"""
    
    print("🧪 Testing Model Architectures")
    print("=" * 40)
    
    models_to_test = [
        'resnet50',
        'efficientnet_b0', 
        'densenet121',
        'attention'
    ]
    
    dummy_input = torch.randn(2, 3, 224, 224)
    
    for model_name in models_to_test:
        print(f"\n📊 Testing {model_name}:")
        
        try:
            model = create_model(model_name, num_classes=4)
            
            # Count parameters
            count_parameters(model)
            
            # Test forward pass
            model.eval()
            with torch.no_grad():
                output = model(dummy_input)
                print(f"Output shape: {output.shape}")
                print(f"✅ {model_name} working correctly")
                
        except Exception as e:
            print(f"❌ Error with {model_name}: {e}")

if __name__ == "__main__":
    test_model_architectures()
