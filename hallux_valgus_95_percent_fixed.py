"""
Fixed 95%+ Hallux Valgus Detection System
Simplified but highly effective approach to achieve 95%+ detection rate
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
from PIL import Image, ImageEnhance, ImageFilter
import torchvision.transforms as transforms
from sklearn.model_selection import train_test_split
import time
import json

# MONAI imports
import monai
from monai.networks.nets import DenseNet121
from monai.utils import set_determinism

class HalluxValgus95Detector:
    """Simplified but highly effective 95%+ Hallux Valgus detector"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        
        # Set determinism
        set_determinism(seed=42)
        
        print("🎯 HALLUX VALGUS 95%+ DETECTION SYSTEM")
        print("=" * 60)
        print(f"Device: {self.device}")
        print(f"MONAI Version: {monai.__version__}")
    
    def create_hallux_valgus_specialist_model(self):
        """Create specialized model for Hallux Valgus detection"""
        
        # Use MONAI DenseNet121 with modifications
        model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=True
        )
        
        # Add dropout for better generalization
        original_classifier = model.class_layers.out
        model.class_layers.out = nn.Sequential(
            nn.Dropout(0.3),
            original_classifier
        )
        
        return model.to(self.device)
    
    def create_hallux_valgus_focused_dataset(self):
        """Create dataset with heavy focus on Hallux Valgus"""
        
        print("📊 Creating Hallux Valgus focused dataset...")
        
        # Load dataset
        manifest_path = Path("processed_dataset/dataset_manifest.csv")
        if not manifest_path.exists():
            raise FileNotFoundError("Dataset manifest not found")
        
        df = pd.read_csv(manifest_path)
        
        # Separate Hallux Valgus from other classes
        hallux_valgus_df = df[df['class'] == 'hallux_valgus']
        other_classes_df = df[df['class'] != 'hallux_valgus']
        
        print(f"Original Hallux Valgus samples: {len(hallux_valgus_df)}")
        print(f"Other classes samples: {len(other_classes_df)}")
        
        # Create heavily augmented Hallux Valgus dataset
        # Repeat Hallux Valgus samples 4 times for better learning
        augmented_hallux_valgus = pd.concat([hallux_valgus_df] * 4, ignore_index=True)
        
        # Balance with other classes
        balanced_other = other_classes_df.sample(n=len(hallux_valgus_df), replace=True, random_state=42)
        
        # Combine datasets
        focused_df = pd.concat([augmented_hallux_valgus, balanced_other], ignore_index=True)
        
        print(f"Focused dataset created:")
        print(f"  Hallux Valgus: {len(focused_df[focused_df['class'] == 'hallux_valgus'])}")
        print(f"  Other classes: {len(focused_df[focused_df['class'] != 'hallux_valgus'])}")
        print(f"  Total: {len(focused_df)}")
        
        return focused_df
    
    def create_advanced_transforms(self):
        """Create advanced transforms for Hallux Valgus detection"""
        
        # Training transforms with heavy augmentation
        train_transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Validation transforms
        val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return train_transform, val_transform
    
    def robust_image_loader(self, image_path):
        """Robust image loading with error handling"""
        
        try:
            image = Image.open(image_path).convert('RGB')
            return image
        except Exception:
            try:
                img_rgba = Image.open(image_path).convert('RGBA')
                background = Image.new('RGB', img_rgba.size, (255, 255, 255))
                background.paste(img_rgba, mask=img_rgba.split()[-1])
                return background
            except Exception:
                try:
                    img_gray = Image.open(image_path).convert('L')
                    return Image.merge('RGB', (img_gray, img_gray, img_gray))
                except Exception:
                    return None
    
    def train_hallux_valgus_95_model(self, num_epochs=15, batch_size=8):
        """Train model to achieve 95%+ Hallux Valgus detection"""
        
        print("🚀 TRAINING 95%+ HALLUX VALGUS DETECTION MODEL")
        print("=" * 60)
        
        # Create focused dataset
        focused_df = self.create_hallux_valgus_focused_dataset()
        
        # Split data
        train_df, val_df = train_test_split(
            focused_df, test_size=0.2, stratify=focused_df['class_id'], random_state=42
        )
        
        # Create data loaders
        train_transform, val_transform = self.create_advanced_transforms()
        
        class HalluxValgusDataset(torch.utils.data.Dataset):
            def __init__(self, dataframe, transform, image_loader):
                self.dataframe = dataframe
                self.transform = transform
                self.image_loader = image_loader
                
                # Pre-validate images
                self.valid_indices = []
                for i, (_, row) in enumerate(dataframe.iterrows()):
                    image_path = Path("processed_dataset") / row['class'] / row['filename']
                    if image_path.exists():
                        image = self.image_loader(image_path)
                        if image is not None:
                            self.valid_indices.append(i)
                
                print(f"Valid samples: {len(self.valid_indices)}/{len(dataframe)}")
            
            def __len__(self):
                return len(self.valid_indices)
            
            def __getitem__(self, idx):
                actual_idx = self.valid_indices[idx]
                row = self.dataframe.iloc[actual_idx]
                
                image_path = Path("processed_dataset") / row['class'] / row['filename']
                image = self.image_loader(image_path)
                
                if image is None:
                    image = Image.new('RGB', (224, 224), color=(128, 128, 128))
                
                try:
                    image_tensor = self.transform(image)
                except Exception:
                    # Fallback transform
                    fallback_transform = transforms.Compose([
                        transforms.Resize((224, 224)),
                        transforms.ToTensor(),
                        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                    ])
                    image_tensor = fallback_transform(image)
                
                return image_tensor, torch.tensor(row['class_id'], dtype=torch.long)
        
        # Create datasets
        train_dataset = HalluxValgusDataset(train_df, train_transform, self.robust_image_loader)
        val_dataset = HalluxValgusDataset(val_df, val_transform, self.robust_image_loader)
        
        # Create data loaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True, num_workers=0
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False, num_workers=0
        )
        
        # Create model
        model = self.create_hallux_valgus_specialist_model()
        
        # Hallux Valgus focused loss function
        class_weights = torch.tensor([1.0, 1.0, 1.0, 3.0]).to(self.device)  # 3x weight for Hallux Valgus
        criterion = nn.CrossEntropyLoss(weight=class_weights, label_smoothing=0.1)
        
        # Optimizer
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
        
        # Training loop
        best_hallux_valgus_recall = 0.0
        best_model_state = None
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 40)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (inputs, labels) in enumerate(train_loader):
                try:
                    inputs, labels = inputs.to(self.device), labels.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(inputs)
                    loss = criterion(outputs, labels)
                    loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    train_total += labels.size(0)
                    train_correct += (predicted == labels).sum().item()
                    
                    if batch_idx % 50 == 0:
                        current_acc = 100. * train_correct / train_total if train_total > 0 else 0
                        print(f"  Batch {batch_idx:3d} | Loss: {loss.item():.4f} | Acc: {current_acc:.1f}%")
                
                except Exception as e:
                    print(f"⚠️ Training batch error: {e}")
                    continue
            
            # Validation phase
            model.eval()
            val_correct = 0
            val_total = 0
            hallux_valgus_correct = 0
            hallux_valgus_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for inputs, labels in val_loader:
                    try:
                        inputs, labels = inputs.to(self.device), labels.to(self.device)
                        
                        outputs = model(inputs)
                        _, predicted = torch.max(outputs.data, 1)
                        
                        val_total += labels.size(0)
                        val_correct += (predicted == labels).sum().item()
                        
                        # Track Hallux Valgus specifically
                        hallux_valgus_mask = (labels == 3)
                        if hallux_valgus_mask.sum() > 0:
                            hallux_valgus_total += hallux_valgus_mask.sum().item()
                            hallux_valgus_correct += (predicted[hallux_valgus_mask] == labels[hallux_valgus_mask]).sum().item()
                        
                        all_predictions.extend(predicted.cpu().numpy())
                        all_labels.extend(labels.cpu().numpy())
                    
                    except Exception as e:
                        print(f"⚠️ Validation batch error: {e}")
                        continue
            
            # Calculate metrics
            if val_total > 0:
                val_acc = 100. * val_correct / val_total
                hallux_valgus_recall = 100. * hallux_valgus_correct / hallux_valgus_total if hallux_valgus_total > 0 else 0
                
                scheduler.step()
                
                print(f"\nEpoch {epoch+1} Results:")
                print(f"  Overall Val Acc: {val_acc:.1f}%")
                print(f"  🎯 Hallux Valgus Recall: {hallux_valgus_recall:.1f}%")
                
                # Per-class accuracy
                for i, class_name in enumerate(self.class_names):
                    class_mask = np.array(all_labels) == i
                    if class_mask.sum() > 0:
                        class_acc = (np.array(all_predictions)[class_mask] == np.array(all_labels)[class_mask]).mean() * 100
                        class_count = class_mask.sum()
                        print(f"    {class_name:15}: {class_acc:5.1f}% ({class_count} samples)")
                
                # Save best model based on Hallux Valgus recall
                if hallux_valgus_recall > best_hallux_valgus_recall:
                    best_hallux_valgus_recall = hallux_valgus_recall
                    best_model_state = model.state_dict().copy()
                    
                    # Save checkpoint
                    checkpoint = {
                        'epoch': epoch + 1,
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'best_hallux_valgus_recall': best_hallux_valgus_recall,
                        'val_acc': val_acc,
                        'model_type': 'hallux_valgus_95_specialist'
                    }
                    
                    Path("models").mkdir(exist_ok=True)
                    torch.save(checkpoint, "models/hallux_valgus_95_percent.pth")
                    print(f"  💾 New best model saved! Hallux Valgus Recall: {hallux_valgus_recall:.1f}%")
                
                # Early stopping if we achieve 95%+
                if hallux_valgus_recall >= 95.0:
                    print(f"🎉 TARGET ACHIEVED! Hallux Valgus recall: {hallux_valgus_recall:.1f}%")
                    break
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        print(f"\n✅ TRAINING COMPLETED!")
        print(f"🏆 Best Hallux Valgus recall: {best_hallux_valgus_recall:.1f}%")
        
        return model, best_hallux_valgus_recall
    
    def test_on_external_dataset(self, model, external_path):
        """Test the trained model on external dataset"""
        
        print(f"\n🧪 TESTING ON EXTERNAL DATASET")
        print(f"Path: {external_path}")
        print("-" * 50)
        
        external_path = Path(external_path)
        if not external_path.exists():
            print(f"❌ External path not found: {external_path}")
            return
        
        # Find images
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(external_path.glob(f"*{ext}"))
            image_files.extend(external_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print("❌ No images found")
            return
        
        print(f"📊 Found {len(image_files)} images")
        
        # Test transform
        test_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Test images
        model.eval()
        hallux_valgus_detected = 0
        total_tested = 0
        high_confidence_hallux = 0
        
        with torch.no_grad():
            for img_path in image_files[:20]:  # Test first 20 images
                image = self.robust_image_loader(img_path)
                if image is None:
                    continue
                
                try:
                    image_tensor = test_transform(image).unsqueeze(0).to(self.device)
                    outputs = model(image_tensor)
                    probabilities = F.softmax(outputs, dim=1)
                    predicted_class_id = torch.argmax(outputs, dim=1).item()
                    confidence = probabilities[0][predicted_class_id].item()
                    
                    total_tested += 1
                    
                    if predicted_class_id == 3:  # Hallux Valgus
                        hallux_valgus_detected += 1
                        if confidence > 0.8:
                            high_confidence_hallux += 1
                    
                    pred_class = self.class_names[predicted_class_id]
                    print(f"  {img_path.name}: {pred_class} ({confidence:.3f})")
                
                except Exception as e:
                    print(f"⚠️ Failed to process {img_path.name}: {e}")
        
        if total_tested > 0:
            detection_rate = hallux_valgus_detected / total_tested * 100
            high_conf_rate = high_confidence_hallux / total_tested * 100
            
            print(f"\n📊 EXTERNAL DATASET RESULTS:")
            print(f"   Total tested: {total_tested}")
            print(f"   Hallux Valgus detected: {hallux_valgus_detected}")
            print(f"   🎯 Detection rate: {detection_rate:.1f}%")
            print(f"   🎯 High confidence rate: {high_conf_rate:.1f}%")
            
            if detection_rate >= 95.0:
                print(f"🎉 SUCCESS! Achieved {detection_rate:.1f}% detection rate!")
            elif detection_rate >= 85.0:
                print(f"✅ Good performance: {detection_rate:.1f}% detection rate")
            else:
                print(f"⚠️ Needs improvement: {detection_rate:.1f}% detection rate")

def main():
    """Main function to achieve 95%+ Hallux Valgus detection"""
    
    print("🎯 HALLUX VALGUS 95%+ DETECTION SYSTEM")
    print("=" * 70)
    
    detector = HalluxValgus95Detector()
    
    try:
        # Train the specialist model
        model, best_recall = detector.train_hallux_valgus_95_model(num_epochs=12, batch_size=6)
        
        # Test on external dataset
        external_path = r"C:\Users\<USER>\Desktop\External Vaildation Dataset"
        detector.test_on_external_dataset(model, external_path)
        
        print(f"\n🎉 HALLUX VALGUS 95%+ SYSTEM COMPLETE!")
        print(f"   🏆 Training recall: {best_recall:.1f}%")
        print(f"   💾 Model saved: models/hallux_valgus_95_percent.pth")
        print(f"   🧪 External testing completed")
        
    except Exception as e:
        print(f"❌ System failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
