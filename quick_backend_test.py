"""
Quick test to verify backend is working
"""

import subprocess
import sys

def test_backend():
    """Quick backend test"""
    print("🧪 Quick Backend Test")
    print("=" * 30)
    
    try:
        # Test with curl
        result = subprocess.run([
            "curl", "-s", "http://localhost:8000/health"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Backend is responding")
            print(f"Response: {result.stdout}")
        else:
            print("❌ Backend not responding")
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_backend()
