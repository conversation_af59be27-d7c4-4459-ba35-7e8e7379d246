"""
Test the API fix with real image
"""

import requests
from pathlib import Path

def test_api_prediction():
    """Test API prediction with real image"""
    
    print("🧪 Testing API Prediction Fix")
    print("=" * 40)
    
    # Find a test image
    processed_dataset = Path("processed_dataset")
    test_image = None
    
    for class_name in ['flatfoot', 'foot_ulcer', 'hallux_valgus', 'normal']:
        class_dir = processed_dataset / class_name
        if class_dir.exists():
            image_files = list(class_dir.glob("*.jpg"))
            if image_files:
                test_image = image_files[0]
                true_class = class_name
                break
    
    if not test_image:
        print("❌ No test images found")
        return False
    
    print(f"📸 Testing with: {test_image}")
    print(f"🎯 True class: {true_class}")
    
    try:
        # Test API prediction
        with open(test_image, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/predict', files=files, timeout=30)
        
        print(f"📡 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ API Prediction Successful!")
            print(f"🎯 Predicted: {result['predicted_class']}")
            print(f"📊 Confidence: {result['confidence']:.1%}")
            print(f"⚡ Processing time: {result['inference_time']*1000:.1f}ms")
            print(f"🔍 Correct: {'✅' if result['predicted_class'] == true_class else '❌'}")
            
            print(f"\n📈 All probabilities:")
            for class_name, prob in sorted(result['all_probabilities'].items(), key=lambda x: x[1], reverse=True):
                print(f"   {class_name:15}: {prob:.1%}")
            
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_api_prediction()
    
    if success:
        print(f"\n🎉 API FIX SUCCESSFUL!")
        print(f"✅ Your FootAI API is now working with real predictions!")
    else:
        print(f"\n❌ API still has issues")
