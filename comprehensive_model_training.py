"""
Comprehensive Model Training and Evaluation System
Train and compare EfficientNet, MONAI, ResNet50, ViT, and InceptionNet
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.models as models
import pandas as pd
import numpy as np
from pathlib import Path
from PIL import Image
import time
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# MONAI imports
import monai
from monai.networks.nets import DenseNet121, EfficientNetBN
from monai.utils import set_determinism

# Vision Transformer
try:
    import timm
    VIT_AVAILABLE = True
except ImportError:
    VIT_AVAILABLE = False
    print("⚠️ timm not available. Installing...")
    import subprocess
    subprocess.check_call(["pip", "install", "timm"])
    import timm
    VIT_AVAILABLE = True

class ComprehensiveModelTrainer:
    """Comprehensive training system for multiple architectures"""
    
    def __init__(self, epochs=32):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.epochs = epochs
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.num_classes = len(self.class_names)
        self.results = {}
        
        # Set determinism for reproducible results
        set_determinism(seed=42)
        torch.manual_seed(42)
        np.random.seed(42)
        
        print(f"🚀 COMPREHENSIVE MODEL TRAINING SYSTEM")
        print(f"=" * 70)
        print(f"Device: {self.device}")
        print(f"Epochs: {self.epochs}")
        print(f"Classes: {self.num_classes}")
        print(f"MONAI Version: {monai.__version__}")
        if VIT_AVAILABLE:
            print(f"TIMM Available: ✅")
        
    def create_models(self):
        """Create all model architectures"""
        
        models_dict = {}
        
        print(f"\n🏗️ CREATING MODEL ARCHITECTURES")
        print("-" * 50)
        
        # 1. EfficientNet (using MONAI)
        try:
            models_dict['EfficientNet'] = EfficientNetBN(
                'efficientnet-b3',
                spatial_dims=2,
                in_channels=3,
                num_classes=self.num_classes,
                pretrained=True
            )
            print("✅ EfficientNet-B3 (MONAI) created")
        except Exception as e:
            print(f"❌ EfficientNet failed: {e}")
        
        # 2. MONAI DenseNet121
        try:
            models_dict['MONAI_DenseNet'] = DenseNet121(
                spatial_dims=2,
                in_channels=3,
                out_channels=self.num_classes,
                pretrained=True
            )
            print("✅ MONAI DenseNet121 created")
        except Exception as e:
            print(f"❌ MONAI DenseNet failed: {e}")
        
        # 3. ResNet50
        try:
            resnet = models.resnet50(pretrained=True)
            resnet.fc = nn.Linear(resnet.fc.in_features, self.num_classes)
            models_dict['ResNet50'] = resnet
            print("✅ ResNet50 created")
        except Exception as e:
            print(f"❌ ResNet50 failed: {e}")
        
        # 4. Vision Transformer (ViT)
        if VIT_AVAILABLE:
            try:
                vit = timm.create_model('vit_base_patch16_224', pretrained=True, num_classes=self.num_classes)
                models_dict['ViT'] = vit
                print("✅ Vision Transformer (ViT) created")
            except Exception as e:
                print(f"❌ ViT failed: {e}")
        
        # 5. InceptionNet
        try:
            inception = models.inception_v3(pretrained=True, aux_logits=False)
            inception.fc = nn.Linear(inception.fc.in_features, self.num_classes)
            models_dict['InceptionNet'] = inception
            print("✅ InceptionNet created")
        except Exception as e:
            print(f"❌ InceptionNet failed: {e}")
        
        print(f"\n📊 Total models created: {len(models_dict)}")
        return models_dict
    
    def create_data_loaders(self, batch_size=16):
        """Create data loaders for training and validation"""
        
        print(f"\n📊 CREATING DATA LOADERS")
        print("-" * 50)
        
        # Load dataset
        manifest_path = Path("processed_dataset/dataset_manifest.csv")
        if not manifest_path.exists():
            raise FileNotFoundError("Dataset manifest not found")
        
        df = pd.read_csv(manifest_path)
        print(f"Total samples: {len(df)}")
        
        # Class distribution
        class_counts = df['class'].value_counts()
        print("Class distribution:")
        for class_name, count in class_counts.items():
            print(f"  {class_name}: {count}")
        
        # Split data
        train_df, val_df = train_test_split(
            df, test_size=0.2, stratify=df['class_id'], random_state=42
        )
        
        print(f"Training samples: {len(train_df)}")
        print(f"Validation samples: {len(val_df)}")
        
        # Create transforms
        train_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Create datasets
        train_dataset = FootDataset(train_df, train_transform)
        val_dataset = FootDataset(val_df, val_transform)
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True, num_workers=0
        )
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False, num_workers=0
        )
        
        return train_loader, val_loader
    
    def train_model(self, model, model_name, train_loader, val_loader):
        """Train a single model"""
        
        print(f"\n🚀 TRAINING {model_name.upper()}")
        print("=" * 60)
        
        model = model.to(self.device)
        
        # Loss function with class weights for imbalanced dataset
        class_weights = torch.tensor([1.0, 2.0, 3.0, 2.5]).to(self.device)  # Adjust based on class frequency
        criterion = nn.CrossEntropyLoss(weight=class_weights)
        
        # Optimizer
        optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.epochs)
        
        # Training history
        history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'hallux_valgus_recall': []
        }
        
        best_val_acc = 0.0
        best_hv_recall = 0.0
        best_model_state = None
        
        start_time = time.time()
        
        for epoch in range(self.epochs):
            print(f"\nEpoch {epoch+1}/{self.epochs}")
            print("-" * 40)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (inputs, labels) in enumerate(train_loader):
                try:
                    inputs, labels = inputs.to(self.device), labels.to(self.device)
                    
                    optimizer.zero_grad()
                    
                    # Handle InceptionNet auxiliary outputs
                    if model_name == 'InceptionNet' and model.training:
                        outputs, aux_outputs = model(inputs)
                        loss1 = criterion(outputs, labels)
                        loss2 = criterion(aux_outputs, labels)
                        loss = loss1 + 0.4 * loss2
                    else:
                        outputs = model(inputs)
                        loss = criterion(outputs, labels)
                    
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    train_total += labels.size(0)
                    train_correct += (predicted == labels).sum().item()
                    
                    if batch_idx % 50 == 0:
                        current_acc = 100. * train_correct / train_total if train_total > 0 else 0
                        print(f"  Batch {batch_idx:3d} | Loss: {loss.item():.4f} | Acc: {current_acc:.1f}%")
                
                except Exception as e:
                    print(f"⚠️ Training batch error: {e}")
                    continue
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for inputs, labels in val_loader:
                    try:
                        inputs, labels = inputs.to(self.device), labels.to(self.device)
                        outputs = model(inputs)
                        loss = criterion(outputs, labels)
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += labels.size(0)
                        val_correct += (predicted == labels).sum().item()
                        
                        all_predictions.extend(predicted.cpu().numpy())
                        all_labels.extend(labels.cpu().numpy())
                    
                    except Exception as e:
                        print(f"⚠️ Validation batch error: {e}")
                        continue
            
            # Calculate metrics
            if val_total > 0:
                train_acc = 100. * train_correct / train_total
                val_acc = 100. * val_correct / val_total
                
                # Calculate Hallux Valgus recall
                hv_mask = np.array(all_labels) == 3
                if hv_mask.sum() > 0:
                    hv_correct = (np.array(all_predictions)[hv_mask] == np.array(all_labels)[hv_mask]).sum()
                    hv_recall = 100. * hv_correct / hv_mask.sum()
                else:
                    hv_recall = 0.0
                
                # Update history
                history['train_loss'].append(train_loss / len(train_loader))
                history['train_acc'].append(train_acc)
                history['val_loss'].append(val_loss / len(val_loader))
                history['val_acc'].append(val_acc)
                history['hallux_valgus_recall'].append(hv_recall)
                
                scheduler.step()
                
                print(f"\nEpoch {epoch+1} Results:")
                print(f"  Train Acc: {train_acc:.1f}% | Val Acc: {val_acc:.1f}%")
                print(f"  🦶 Hallux Valgus Recall: {hv_recall:.1f}%")
                
                # Per-class accuracy
                for i, class_name in enumerate(self.class_names):
                    class_mask = np.array(all_labels) == i
                    if class_mask.sum() > 0:
                        class_acc = (np.array(all_predictions)[class_mask] == np.array(all_labels)[class_mask]).mean() * 100
                        class_count = class_mask.sum()
                        print(f"    {class_name:15}: {class_acc:5.1f}% ({class_count} samples)")
                
                # Save best model
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    best_hv_recall = hv_recall
                    best_model_state = model.state_dict().copy()
                    
                    # Save checkpoint
                    checkpoint = {
                        'epoch': epoch + 1,
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'best_val_acc': best_val_acc,
                        'best_hv_recall': best_hv_recall,
                        'model_name': model_name,
                        'history': history
                    }
                    
                    Path("models").mkdir(exist_ok=True)
                    torch.save(checkpoint, f"models/{model_name.lower()}_best.pth")
                    print(f"  💾 New best model saved! Val Acc: {val_acc:.1f}%")
        
        training_time = time.time() - start_time
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        print(f"\n✅ {model_name} TRAINING COMPLETED!")
        print(f"🏆 Best Val Acc: {best_val_acc:.1f}%")
        print(f"🦶 Best HV Recall: {best_hv_recall:.1f}%")
        print(f"⏱️ Training Time: {training_time:.1f}s")
        
        return model, {
            'best_val_acc': best_val_acc,
            'best_hv_recall': best_hv_recall,
            'training_time': training_time,
            'history': history
        }
    
    def evaluate_model(self, model, model_name, val_loader):
        """Comprehensive evaluation of a trained model"""
        
        print(f"\n📊 EVALUATING {model_name.upper()}")
        print("-" * 50)
        
        model.eval()
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs, labels = inputs.to(self.device), labels.to(self.device)
                outputs = model(inputs)
                probabilities = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # Calculate metrics
        accuracy = (np.array(all_predictions) == np.array(all_labels)).mean() * 100
        
        # Per-class metrics
        class_metrics = {}
        for i, class_name in enumerate(self.class_names):
            class_mask = np.array(all_labels) == i
            if class_mask.sum() > 0:
                class_predictions = np.array(all_predictions)[class_mask]
                class_labels = np.array(all_labels)[class_mask]
                class_acc = (class_predictions == class_labels).mean() * 100
                class_metrics[class_name] = {
                    'accuracy': class_acc,
                    'count': class_mask.sum()
                }
        
        # Hallux Valgus specific metrics
        hv_mask = np.array(all_labels) == 3
        hv_recall = 0.0
        hv_precision = 0.0
        if hv_mask.sum() > 0:
            hv_correct = (np.array(all_predictions)[hv_mask] == 3).sum()
            hv_recall = 100. * hv_correct / hv_mask.sum()
            
            hv_predicted_mask = np.array(all_predictions) == 3
            if hv_predicted_mask.sum() > 0:
                hv_precision = 100. * hv_correct / hv_predicted_mask.sum()
        
        print(f"Overall Accuracy: {accuracy:.1f}%")
        print(f"🦶 Hallux Valgus Recall: {hv_recall:.1f}%")
        print(f"🦶 Hallux Valgus Precision: {hv_precision:.1f}%")
        
        return {
            'accuracy': accuracy,
            'hallux_valgus_recall': hv_recall,
            'hallux_valgus_precision': hv_precision,
            'class_metrics': class_metrics,
            'predictions': all_predictions,
            'labels': all_labels,
            'probabilities': all_probabilities
        }
    
    def run_comprehensive_training(self):
        """Run comprehensive training for all models"""
        
        print(f"\n🚀 STARTING COMPREHENSIVE MODEL TRAINING")
        print("=" * 70)
        
        # Create models
        models_dict = self.create_models()
        
        if not models_dict:
            print("❌ No models created successfully")
            return
        
        # Create data loaders
        train_loader, val_loader = self.create_data_loaders()
        
        # Train each model
        for model_name, model in models_dict.items():
            try:
                trained_model, training_results = self.train_model(
                    model, model_name, train_loader, val_loader
                )
                
                # Evaluate model
                evaluation_results = self.evaluate_model(
                    trained_model, model_name, val_loader
                )
                
                # Store results
                self.results[model_name] = {
                    'training': training_results,
                    'evaluation': evaluation_results
                }
                
            except Exception as e:
                print(f"❌ {model_name} training failed: {e}")
                continue
        
        # Generate comparison report
        self.generate_comparison_report()
    
    def generate_comparison_report(self):
        """Generate comprehensive comparison report"""
        
        print(f"\n📊 COMPREHENSIVE MODEL COMPARISON REPORT")
        print("=" * 70)
        
        if not self.results:
            print("❌ No results to compare")
            return
        
        # Create comparison table
        comparison_data = []
        for model_name, results in self.results.items():
            training = results['training']
            evaluation = results['evaluation']
            
            comparison_data.append({
                'Model': model_name,
                'Val_Accuracy': f"{training['best_val_acc']:.1f}%",
                'HV_Recall': f"{evaluation['hallux_valgus_recall']:.1f}%",
                'HV_Precision': f"{evaluation['hallux_valgus_precision']:.1f}%",
                'Training_Time': f"{training['training_time']:.0f}s"
            })
        
        # Sort by Hallux Valgus recall
        comparison_data.sort(key=lambda x: float(x['HV_Recall'].replace('%', '')), reverse=True)
        
        print("\n🏆 MODEL RANKING (by Hallux Valgus Recall):")
        print("-" * 70)
        print(f"{'Rank':<4} {'Model':<15} {'Val Acc':<10} {'HV Recall':<12} {'HV Precision':<12} {'Time':<10}")
        print("-" * 70)
        
        for i, data in enumerate(comparison_data, 1):
            print(f"{i:<4} {data['Model']:<15} {data['Val_Accuracy']:<10} {data['HV_Recall']:<12} {data['HV_Precision']:<12} {data['Training_Time']:<10}")
        
        # Best model recommendation
        best_model = comparison_data[0]
        print(f"\n🥇 BEST MODEL: {best_model['Model']}")
        print(f"   🎯 Hallux Valgus Recall: {best_model['HV_Recall']}")
        print(f"   📊 Validation Accuracy: {best_model['Val_Accuracy']}")
        print(f"   ⏱️ Training Time: {best_model['Training_Time']}")
        
        # Save results
        results_file = f"model_comparison_results_{int(time.time())}.json"
        with open(results_file, 'w') as f:
            # Convert numpy arrays to lists for JSON serialization
            json_results = {}
            for model_name, results in self.results.items():
                json_results[model_name] = {
                    'training': {
                        'best_val_acc': results['training']['best_val_acc'],
                        'best_hv_recall': results['training']['best_hv_recall'],
                        'training_time': results['training']['training_time']
                    },
                    'evaluation': {
                        'accuracy': results['evaluation']['accuracy'],
                        'hallux_valgus_recall': results['evaluation']['hallux_valgus_recall'],
                        'hallux_valgus_precision': results['evaluation']['hallux_valgus_precision'],
                        'class_metrics': results['evaluation']['class_metrics']
                    }
                }
            
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")

class FootDataset(Dataset):
    """Dataset class for foot images"""
    
    def __init__(self, dataframe, transform=None):
        self.dataframe = dataframe
        self.transform = transform
        
        # Pre-validate images
        self.valid_indices = []
        for i, (_, row) in enumerate(dataframe.iterrows()):
            image_path = Path("processed_dataset") / row['class'] / row['filename']
            if image_path.exists():
                self.valid_indices.append(i)
    
    def __len__(self):
        return len(self.valid_indices)
    
    def __getitem__(self, idx):
        actual_idx = self.valid_indices[idx]
        row = self.dataframe.iloc[actual_idx]
        
        image_path = Path("processed_dataset") / row['class'] / row['filename']
        
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception:
            # Fallback to a blank image
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        if self.transform:
            image = self.transform(image)
        
        return image, torch.tensor(row['class_id'], dtype=torch.long)

def main():
    """Main function to run comprehensive training"""
    
    import argparse
    parser = argparse.ArgumentParser(description='Comprehensive Model Training')
    parser.add_argument('--epochs', type=int, default=32, choices=[32, 64], 
                       help='Number of epochs (32 or 64)')
    args = parser.parse_args()
    
    try:
        trainer = ComprehensiveModelTrainer(epochs=args.epochs)
        trainer.run_comprehensive_training()
        
        print(f"\n🎉 COMPREHENSIVE TRAINING COMPLETED!")
        print(f"   📊 Models trained and evaluated")
        print(f"   🏆 Best model identified")
        print(f"   💾 Results saved")
        
    except Exception as e:
        print(f"❌ Comprehensive training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
