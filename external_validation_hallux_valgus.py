"""
External Validation for Hallux Valgus Dataset
Specific validation for your external dataset at C:/Users/<USER>/Desktop/External Vaildation Dataset
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns
import time
import json
import os

# MONAI imports
import monai
from monai.networks.nets import DenseNet121

class HalluxValgusExternalValidator:
    """External validation specifically for Hallux Valgus dataset"""
    
    def __init__(self, model_path="models/monai_densenet121_simple.pth"):
        self.model_path = Path(model_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.model = None
        self.transform = None
        
        print("🦶 HALLUX VALGUS EXTERNAL VALIDATION")
        print("=" * 60)
        print(f"Device: {self.device}")
        print(f"MONAI Version: {monai.__version__}")
        print(f"Target Dataset: C:/Users/<USER>/Desktop/External Vaildation Dataset")
    
    def load_monai_model(self):
        """Load the trained MONAI model"""
        
        if not self.model_path.exists():
            raise FileNotFoundError(f"MONAI model not found: {self.model_path}")
        
        print(f"📦 Loading MONAI model from {self.model_path}")
        
        # Create model architecture
        self.model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=False
        )
        
        # Load checkpoint with error handling
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            # Get model info
            best_val_acc = checkpoint.get('best_val_acc', 0)
            print(f"✅ MONAI model loaded successfully")
            print(f"📊 Training validation accuracy: {best_val_acc:.1f}%")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            print("🔧 Trying alternative loading method...")
            
            # Alternative loading method
            try:
                checkpoint = torch.load(self.model_path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.to(self.device)
                self.model.eval()
                print("✅ Model loaded with alternative method")
            except Exception as e2:
                raise Exception(f"Failed to load model with both methods: {e}, {e2}")
        
        # Setup robust preprocessing
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        print("✅ Preprocessing pipeline configured")
    
    def robust_image_loading(self, image_path):
        """Ultra-robust image loading with multiple fallback methods"""
        
        try:
            # Method 1: Standard PIL RGB
            image = Image.open(image_path).convert('RGB')
            
            # Validate image
            if image.size[0] < 10 or image.size[1] < 10:
                print(f"⚠️ Very small image: {image.size}")
                return None
            
            return image
            
        except Exception as e1:
            print(f"⚠️ Method 1 failed for {image_path}: {e1}")
            
            try:
                # Method 2: RGBA to RGB conversion
                image = Image.open(image_path).convert('RGBA')
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1] if len(image.split()) == 4 else None)
                print(f"✅ RGBA conversion successful for {image_path}")
                return background
                
            except Exception as e2:
                print(f"⚠️ Method 2 failed for {image_path}: {e2}")
                
                try:
                    # Method 3: Grayscale to RGB
                    image = Image.open(image_path).convert('L')
                    rgb_image = Image.merge('RGB', (image, image, image))
                    print(f"✅ Grayscale conversion successful for {image_path}")
                    return rgb_image
                    
                except Exception as e3:
                    print(f"❌ All methods failed for {image_path}: {e3}")
                    return None
    
    def predict_image(self, image_path):
        """Predict single image with comprehensive error handling"""
        
        # Load image
        image = self.robust_image_loading(image_path)
        if image is None:
            return None
        
        try:
            # Apply transforms
            image_tensor = self.transform(image)
            
            # Check for NaN values
            if torch.isnan(image_tensor).any():
                print(f"⚠️ NaN values detected in {image_path}, replacing with zeros")
                image_tensor = torch.nan_to_num(image_tensor, nan=0.0)
            
            # Add batch dimension and move to device
            image_tensor = image_tensor.unsqueeze(0).to(self.device)
            
            # Make prediction
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = F.softmax(outputs, dim=1)
                predicted_class_id = torch.argmax(outputs, dim=1).item()
                confidence = probabilities[0][predicted_class_id].item()
            
            return {
                'image_path': str(image_path),
                'predicted_class_id': predicted_class_id,
                'predicted_class': self.class_names[predicted_class_id],
                'confidence': confidence,
                'probabilities': probabilities[0].cpu().numpy().tolist()
            }
            
        except Exception as e:
            print(f"❌ Prediction failed for {image_path}: {e}")
            return None
    
    def validate_external_dataset(self, dataset_path):
        """Validate external Hallux Valgus dataset"""
        
        dataset_path = Path(dataset_path)
        print(f"\n🔍 EXTERNAL VALIDATION")
        print(f"Dataset path: {dataset_path}")
        print("-" * 50)
        
        # Check if path exists
        if not dataset_path.exists():
            print(f"❌ Dataset path does not exist: {dataset_path}")
            print(f"🔧 Please check the path and try again")
            return None
        
        # Find all image files
        print(f"🔍 Searching for images in {dataset_path}...")
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        all_images = []
        
        # Search recursively for images
        for root, dirs, files in os.walk(dataset_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    all_images.append(Path(root) / file)
        
        if not all_images:
            print(f"❌ No images found in {dataset_path}")
            print(f"🔍 Searched for extensions: {image_extensions}")
            return None
        
        print(f"📊 Found {len(all_images)} images")
        
        # Show sample of found images
        print(f"\n📋 Sample images found:")
        for i, img_path in enumerate(all_images[:5]):
            print(f"   {i+1}. {img_path.name}")
        if len(all_images) > 5:
            print(f"   ... and {len(all_images) - 5} more images")
        
        # Process images
        print(f"\n🔄 Processing images...")
        results = []
        successful = 0
        failed = 0
        
        for i, image_path in enumerate(all_images):
            if i % 10 == 0:
                print(f"Progress: {i+1}/{len(all_images)} ({(i+1)/len(all_images)*100:.1f}%)")
            
            # Make prediction
            prediction = self.predict_image(image_path)
            
            if prediction is not None:
                results.append(prediction)
                successful += 1
                
                # Show prediction for first few images
                if i < 10:
                    pred_class = prediction['predicted_class']
                    confidence = prediction['confidence']
                    print(f"   ✅ {image_path.name}: {pred_class} ({confidence:.3f})")
            else:
                failed += 1
                if i < 10:
                    print(f"   ❌ {image_path.name}: Failed to process")
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        
        if successful == 0:
            print("❌ No successful predictions made")
            return None
        
        # Analyze results
        return self.analyze_results(results, dataset_path)
    
    def analyze_results(self, results, dataset_path):
        """Analyze external validation results"""
        
        print(f"\n📊 EXTERNAL VALIDATION ANALYSIS")
        print("=" * 50)
        
        total_predictions = len(results)
        
        # Overall statistics
        confidences = [r['confidence'] for r in results]
        avg_confidence = np.mean(confidences)
        
        print(f"Total predictions: {total_predictions}")
        print(f"Average confidence: {avg_confidence:.3f}")
        
        # Confidence distribution
        high_conf = sum(1 for c in confidences if c > 0.8)
        med_conf = sum(1 for c in confidences if 0.5 <= c <= 0.8)
        low_conf = sum(1 for c in confidences if c < 0.5)
        
        print(f"\nConfidence Distribution:")
        print(f"   🟢 High (>80%): {high_conf} ({high_conf/total_predictions*100:.1f}%)")
        print(f"   🟡 Medium (50-80%): {med_conf} ({med_conf/total_predictions*100:.1f}%)")
        print(f"   🔴 Low (<50%): {low_conf} ({low_conf/total_predictions*100:.1f}%)")
        
        # Class distribution
        print(f"\nPredicted Class Distribution:")
        class_counts = {}
        for class_name in self.class_names:
            count = sum(1 for r in results if r['predicted_class'] == class_name)
            class_counts[class_name] = count
            percentage = count/total_predictions*100
            print(f"   {class_name:15}: {count:4d} ({percentage:5.1f}%)")
        
        # Expected class analysis (since this is Hallux Valgus dataset)
        hallux_valgus_predictions = class_counts.get('hallux_valgus', 0)
        hallux_valgus_percentage = hallux_valgus_predictions / total_predictions * 100
        
        print(f"\n🦶 HALLUX VALGUS SPECIFIC ANALYSIS:")
        print(f"   Expected class: Hallux Valgus")
        print(f"   Predicted as Hallux Valgus: {hallux_valgus_predictions}/{total_predictions} ({hallux_valgus_percentage:.1f}%)")
        
        if hallux_valgus_percentage > 70:
            print(f"   ✅ Good detection rate for Hallux Valgus")
        elif hallux_valgus_percentage > 50:
            print(f"   ⚠️ Moderate detection rate for Hallux Valgus")
        else:
            print(f"   ❌ Low detection rate for Hallux Valgus - check image quality")
        
        # High confidence Hallux Valgus predictions
        high_conf_hallux = [r for r in results if r['predicted_class'] == 'hallux_valgus' and r['confidence'] > 0.8]
        print(f"   🎯 High confidence Hallux Valgus: {len(high_conf_hallux)} ({len(high_conf_hallux)/total_predictions*100:.1f}%)")
        
        # Save results
        self.save_validation_results(results, dataset_path)
        
        return {
            'total_predictions': total_predictions,
            'average_confidence': avg_confidence,
            'confidence_distribution': {'high': high_conf, 'medium': med_conf, 'low': low_conf},
            'class_distribution': class_counts,
            'hallux_valgus_detection_rate': hallux_valgus_percentage,
            'high_confidence_hallux_valgus': len(high_conf_hallux),
            'results': results
        }
    
    def save_validation_results(self, results, dataset_path):
        """Save validation results"""
        
        # Create results directory
        results_dir = Path("external_validation_results")
        results_dir.mkdir(exist_ok=True)
        
        # Save detailed results
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"hallux_valgus_validation_{timestamp}.json"
        
        validation_data = {
            'dataset_path': str(dataset_path),
            'timestamp': timestamp,
            'total_images': len(results),
            'model_info': {
                'architecture': 'MONAI DenseNet121',
                'model_path': str(self.model_path)
            },
            'results': results
        }
        
        with open(results_file, 'w') as f:
            json.dump(validation_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Create summary report
        summary_file = results_dir / f"hallux_valgus_summary_{timestamp}.txt"
        with open(summary_file, 'w') as f:
            f.write("Hallux Valgus External Validation Summary\n")
            f.write("=" * 50 + "\n")
            f.write(f"Dataset: {dataset_path}\n")
            f.write(f"Total images: {len(results)}\n")
            f.write(f"Average confidence: {np.mean([r['confidence'] for r in results]):.3f}\n")
            f.write(f"Timestamp: {timestamp}\n")
            
            # Class distribution
            f.write(f"\nClass Distribution:\n")
            for class_name in self.class_names:
                count = sum(1 for r in results if r['predicted_class'] == class_name)
                f.write(f"  {class_name}: {count}\n")
        
        print(f"📄 Summary saved to: {summary_file}")

def main():
    """Main validation function for Hallux Valgus dataset"""
    
    print("🦶 HALLUX VALGUS EXTERNAL VALIDATION")
    print("=" * 70)
    
    # Your specific dataset path
    external_dataset_path = r"C:\Users\<USER>\Desktop\External Vaildation Dataset"
    
    try:
        # Initialize validator
        validator = HalluxValgusExternalValidator()
        
        # Load model
        validator.load_monai_model()
        
        # Validate external dataset
        print(f"\n🚀 Starting validation of Hallux Valgus dataset...")
        results = validator.validate_external_dataset(external_dataset_path)
        
        if results:
            print(f"\n🎉 EXTERNAL VALIDATION COMPLETED SUCCESSFULLY!")
            print(f"   📊 Total predictions: {results['total_predictions']}")
            print(f"   📈 Average confidence: {results['average_confidence']:.3f}")
            print(f"   🦶 Hallux Valgus detection: {results['hallux_valgus_detection_rate']:.1f}%")
            print(f"   🎯 High confidence Hallux Valgus: {results['high_confidence_hallux_valgus']}")
            print(f"   💾 Results saved in 'external_validation_results' directory")
        else:
            print(f"\n❌ External validation failed")
    
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"1. Check if path exists: {external_dataset_path}")
        print(f"2. Verify MONAI model exists: models/monai_densenet121_simple.pth")
        print(f"3. Ensure images are in supported formats (jpg, png, etc.)")
        print(f"4. Check file permissions")

if __name__ == "__main__":
    main()
