"""
Simple HTTP server to serve the demo.html file
This avoids CORS issues with file:// protocol
"""

import http.server
import socketserver
import webbrowser
import os
from pathlib import Path

PORT = 3001

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

def main():
    # Change to the directory containing demo.html
    demo_dir = Path(__file__).parent
    os.chdir(demo_dir)
    
    print(f"🌐 Starting Demo Server...")
    print(f"📁 Serving from: {demo_dir}")
    print(f"🔗 Demo URL: http://localhost:{PORT}/demo.html")
    print(f"🚀 Backend API: http://localhost:8000")
    print(f"📚 API Docs: http://localhost:8000/docs")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Demo server running on port {PORT}")
            
            # Open browser automatically
            webbrowser.open(f'http://localhost:{PORT}/demo.html')
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Demo server stopped")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} is already in use. Try a different port.")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
