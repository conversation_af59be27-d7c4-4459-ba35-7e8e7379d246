"""
Dataset-Based Model Recommendation for Optimal Accuracy
Analyze your specific dataset and recommend the best model for internal/external validation
"""

import pandas as pd
import numpy as np
from pathlib import Path
import torch

def analyze_dataset():
    """Analyze the foot deformity dataset"""
    
    print("📊 DATASET ANALYSIS FOR OPTIMAL MODEL SELECTION")
    print("=" * 60)
    
    # Load dataset manifest
    manifest_path = Path("processed_dataset/dataset_manifest.csv")
    if not manifest_path.exists():
        print("❌ Dataset manifest not found")
        return None
    
    df = pd.read_csv(manifest_path)
    
    print(f"📈 DATASET CHARACTERISTICS:")
    print(f"   Total Images: {len(df):,}")
    print(f"   Classes: {df['class'].nunique()}")
    
    # Class distribution
    class_dist = df['class'].value_counts()
    print(f"\n📊 CLASS DISTRIBUTION:")
    for class_name, count in class_dist.items():
        percentage = (count / len(df)) * 100
        print(f"   {class_name:15}: {count:4d} images ({percentage:5.1f}%)")
    
    # Calculate imbalance ratio
    max_class = class_dist.max()
    min_class = class_dist.min()
    imbalance_ratio = max_class / min_class
    print(f"\n⚖️ CLASS IMBALANCE RATIO: {imbalance_ratio:.1f}:1")
    
    # Category analysis
    if 'category' in df.columns:
        print(f"\n📂 CATEGORY BREAKDOWN:")
        category_dist = df['category'].value_counts()
        for category, count in category_dist.items():
            percentage = (count / len(df)) * 100
            print(f"   {category:15}: {count:4d} images ({percentage:5.1f}%)")
    
    return df, class_dist, imbalance_ratio

def evaluate_current_models():
    """Evaluate performance of current models"""
    
    print(f"\n🎯 CURRENT MODEL PERFORMANCE:")
    print("-" * 40)
    
    models_dir = Path("models")
    model_results = {}
    
    # Check all available models
    model_files = [
        ("ViT Simple", "vit_simple_best.pth"),
        ("ResNet50 Best", "best_resnet50_model.pth"),
        ("ResNet50 Improved", "improved_resnet50_model.pth"),
        ("Simple Test", "simple_test_model.pth")
    ]
    
    for model_name, filename in model_files:
        model_path = models_dir / filename
        if model_path.exists():
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
                
                # Extract metrics
                val_acc = checkpoint.get('best_val_acc', 0)
                epoch = checkpoint.get('epoch', 'Unknown')
                
                # Estimate parameters based on model type
                if 'vit' in filename.lower():
                    params = '5.2M'
                    interpretability = 'High (Attention + Gradient)'
                    training_type = 'From scratch'
                elif 'resnet' in filename.lower():
                    params = '24.7M'
                    interpretability = 'Medium (Grad-CAM)'
                    training_type = 'Pre-trained + Fine-tuned'
                else:
                    params = 'Unknown'
                    interpretability = 'Basic'
                    training_type = 'Unknown'
                
                model_results[model_name] = {
                    'accuracy': val_acc,
                    'epoch': epoch,
                    'parameters': params,
                    'interpretability': interpretability,
                    'training': training_type,
                    'file_size': model_path.stat().st_size / (1024 * 1024)  # MB
                }
                
                print(f"✅ {model_name:20}: {val_acc:5.1f}% (Epoch {epoch})")
                
            except Exception as e:
                print(f"❌ {model_name:20}: Error loading - {e}")
    
    return model_results

def recommend_optimal_model(df, class_dist, imbalance_ratio, model_results):
    """Recommend optimal model based on dataset and performance"""
    
    print(f"\n🧠 OPTIMAL MODEL RECOMMENDATION:")
    print("=" * 50)
    
    total_images = len(df)
    num_classes = df['class'].nunique()
    
    # Dataset characteristics
    print(f"📊 DATASET ANALYSIS:")
    print(f"   Size: {total_images:,} images")
    print(f"   Classes: {num_classes}")
    print(f"   Imbalance: {imbalance_ratio:.1f}:1")
    
    # Categorize dataset
    if total_images < 1000:
        size_cat = "Small"
    elif total_images < 5000:
        size_cat = "Medium"
    elif total_images < 10000:
        size_cat = "Large"
    else:
        size_cat = "Very Large"
    
    print(f"   Category: {size_cat} dataset")
    
    # Imbalance severity
    if imbalance_ratio < 2:
        imbalance_severity = "Balanced"
    elif imbalance_ratio < 5:
        imbalance_severity = "Moderate imbalance"
    elif imbalance_ratio < 10:
        imbalance_severity = "High imbalance"
    else:
        imbalance_severity = "Severe imbalance"
    
    print(f"   Balance: {imbalance_severity}")
    
    # Performance analysis
    if model_results:
        best_model = max(model_results.items(), key=lambda x: x[1]['accuracy'])
        print(f"\n🏆 BEST PERFORMING MODEL:")
        print(f"   Model: {best_model[0]}")
        print(f"   Accuracy: {best_model[1]['accuracy']:.1f}%")
        print(f"   Parameters: {best_model[1]['parameters']}")
        print(f"   Interpretability: {best_model[1]['interpretability']}")
    
    # Recommendations based on dataset characteristics
    print(f"\n🎯 RECOMMENDATIONS:")
    
    recommendations = []
    
    # For your specific dataset (7,188 images, moderate imbalance)
    if total_images > 5000:
        recommendations.append({
            'rank': 1,
            'model': 'Pre-trained ResNet50',
            'reason': 'Large enough dataset for effective fine-tuning',
            'expected_accuracy': '90-95%',
            'pros': ['Proven medical imaging performance', 'Transfer learning benefits', 'Robust to external images'],
            'cons': ['Limited interpretability', 'Large model size']
        })
        
        recommendations.append({
            'rank': 2,
            'model': 'Ensemble (ResNet50 + ViT)',
            'reason': 'Combine accuracy with interpretability',
            'expected_accuracy': '92-96%',
            'pros': ['Best of both worlds', 'High interpretability', 'Robust predictions'],
            'cons': ['Increased complexity', 'Higher computational cost']
        })
        
        recommendations.append({
            'rank': 3,
            'model': 'Enhanced ViT',
            'reason': 'With more training and data augmentation',
            'expected_accuracy': '75-85%',
            'pros': ['Superior interpretability', 'Efficient architecture', 'Research value'],
            'cons': ['Lower accuracy currently', 'Needs more optimization']
        })
    
    return recommendations

def create_deployment_strategy(recommendations, model_results):
    """Create optimal deployment strategy"""
    
    print(f"\n🚀 DEPLOYMENT STRATEGY:")
    print("=" * 40)
    
    if model_results:
        best_current = max(model_results.items(), key=lambda x: x[1]['accuracy'])
        
        print(f"🎯 IMMEDIATE DEPLOYMENT:")
        print(f"   Primary Model: {best_current[0]}")
        print(f"   Current Accuracy: {best_current[1]['accuracy']:.1f}%")
        print(f"   Reason: Highest validated performance")
        
        print(f"\n📈 OPTIMIZATION STRATEGY:")
        
        if best_current[1]['accuracy'] > 90:
            print(f"   ✅ Production ready - deploy immediately")
            print(f"   ✅ Focus on external validation testing")
            print(f"   ✅ Implement ensemble for interpretability")
        elif best_current[1]['accuracy'] > 80:
            print(f"   ⚠️ Good performance - optimize before deployment")
            print(f"   🔧 Apply advanced data augmentation")
            print(f"   🔧 Implement class balancing techniques")
        else:
            print(f"   ❌ Needs improvement before deployment")
            print(f"   🔧 Increase training epochs")
            print(f"   🔧 Try different architectures")
            print(f"   🔧 Collect more training data")
    
    print(f"\n🔍 EXTERNAL VALIDATION STRATEGY:")
    print(f"   ✅ Test with images from different sources")
    print(f"   ✅ Apply domain adaptation techniques")
    print(f"   ✅ Use conservative confidence thresholds")
    print(f"   ✅ Implement uncertainty quantification")
    
    print(f"\n🏥 CLINICAL DEPLOYMENT:")
    print(f"   ✅ Use ensemble of top 2 models")
    print(f"   ✅ Implement interpretability dashboard")
    print(f"   ✅ Add medical disclaimers")
    print(f"   ✅ Enable human-in-the-loop validation")

def main():
    """Main analysis function"""
    
    print("🦶 OPTIMAL MODEL RECOMMENDATION FOR FOOT DEFORMITY CLASSIFICATION")
    print("=" * 70)
    
    # Analyze dataset
    result = analyze_dataset()
    if result is None:
        return
    
    df, class_dist, imbalance_ratio = result
    
    # Evaluate current models
    model_results = evaluate_current_models()
    
    # Get recommendations
    recommendations = recommend_optimal_model(df, class_dist, imbalance_ratio, model_results)
    
    # Create deployment strategy
    create_deployment_strategy(recommendations, model_results)
    
    # Final summary
    print(f"\n🏆 FINAL RECOMMENDATION SUMMARY:")
    print("=" * 50)
    
    if model_results:
        best_model = max(model_results.items(), key=lambda x: x[1]['accuracy'])
        
        print(f"🥇 BEST MODEL FOR YOUR DATASET:")
        print(f"   Model: {best_model[0]}")
        print(f"   Accuracy: {best_model[1]['accuracy']:.1f}%")
        print(f"   Interpretability: {best_model[1]['interpretability']}")
        
        if best_model[1]['accuracy'] > 90:
            print(f"   Status: ✅ READY FOR PRODUCTION")
        elif best_model[1]['accuracy'] > 80:
            print(f"   Status: ⚠️ NEEDS OPTIMIZATION")
        else:
            print(f"   Status: ❌ REQUIRES IMPROVEMENT")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Deploy best performing model")
    print(f"   2. Test extensively with external images")
    print(f"   3. Implement ensemble for robustness")
    print(f"   4. Add interpretability features")
    print(f"   5. Monitor performance in production")

if __name__ == "__main__":
    main()
