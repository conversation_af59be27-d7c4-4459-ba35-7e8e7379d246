"""
Run 64-Epoch Comprehensive Model Training
Train all models (EfficientNet, MONAI, ResNet50, ViT, InceptionNet) for 64 epochs
"""

import subprocess
import sys
import time
from pathlib import Path

def run_comprehensive_training_64():
    """Run the comprehensive training for 64 epochs"""
    
    print("🚀 STARTING 64-EPOCH COMPREHENSIVE TRAINING")
    print("=" * 70)
    print("Models to train:")
    print("  1. 🔥 EfficientNet-B3 (MONAI)")
    print("  2. 🧠 MONAI DenseNet121")
    print("  3. 🏗️ ResNet50")
    print("  4. 👁️ Vision Transformer (ViT)")
    print("  5. 🌟 InceptionNet")
    print("⚠️ WARNING: 64 epochs will take significantly longer!")
    print("=" * 70)
    
    # Check if dataset exists
    dataset_path = Path("processed_dataset/dataset_manifest.csv")
    if not dataset_path.exists():
        print("❌ Dataset not found. Please ensure processed_dataset exists.")
        return False
    
    # Create models directory
    Path("models").mkdir(exist_ok=True)
    
    # Run training
    try:
        import comprehensive_model_training
        trainer = comprehensive_model_training.ComprehensiveModelTrainer(epochs=64)
        trainer.run_comprehensive_training()
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution function"""
    
    start_time = time.time()
    
    print("🎯 64-EPOCH COMPREHENSIVE MODEL TRAINING")
    print("=" * 70)
    print("This will train and compare 5 different architectures:")
    print("• EfficientNet, MONAI DenseNet, ResNet50, ViT, InceptionNet")
    print("• Each model will be trained for 64 epochs")
    print("• Performance will be compared on Hallux Valgus detection")
    print("⚠️ Expected time: 2-4 hours depending on hardware")
    print("=" * 70)
    
    # Confirmation
    response = input("Continue with 64-epoch training? (y/N): ")
    if response.lower() != 'y':
        print("Training cancelled.")
        return
    
    # Run training
    success = run_comprehensive_training_64()
    
    total_time = time.time() - start_time
    
    if success:
        print(f"\n🎉 64-EPOCH TRAINING COMPLETED SUCCESSFULLY!")
        print(f"⏱️ Total time: {total_time:.1f} seconds ({total_time/3600:.1f} hours)")
        print(f"📊 Check the comparison report for best model")
        print(f"💾 Models saved in 'models/' directory")
        print(f"📋 Results saved in JSON format")
    else:
        print(f"\n❌ Training failed after {total_time:.1f} seconds")
        print(f"🔧 Check error messages above for troubleshooting")

if __name__ == "__main__":
    main()
