"""
Fix Model Accuracy - Comprehensive Training with Better Techniques
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.utils.class_weight import compute_class_weight
from sklearn.metrics import classification_report, confusion_matrix
import time

# Import our modules
from data_preprocessing import DataPreprocessor, FootDeformityDataset
from model_architectures import create_model

class AccuracyFixer:
    """Enhanced trainer to fix model accuracy issues"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        print(f"🚀 Accuracy Fixer initialized on {self.device}")
    
    def diagnose_current_model(self):
        """Diagnose issues with current model"""
        
        print("🔍 DIAGNOSING CURRENT MODEL ISSUES")
        print("=" * 50)
        
        # Load current model
        model_path = 'models/improved_resnet50_model.pth'
        if not Path(model_path).exists():
            print("❌ Improved model not found!")
            return False
        
        checkpoint = torch.load(model_path, map_location=self.device)
        print(f"📊 Current model stats:")
        print(f"   Epochs trained: {checkpoint.get('epoch', 'unknown')}")
        print(f"   Validation accuracy: {checkpoint.get('best_val_acc', 0):.1f}%")
        
        # Check class weights
        if 'class_weights' in checkpoint:
            class_weights = checkpoint['class_weights']
            print(f"   Class weights: {class_weights}")
        
        # Check training history
        if 'history' in checkpoint:
            history = checkpoint['history']
            if history['val_acc']:
                print(f"   Training progress: {len(history['val_acc'])} epochs")
                print(f"   Final train acc: {history['train_acc'][-1]:.1f}%")
                print(f"   Final val acc: {history['val_acc'][-1]:.1f}%")
        
        print(f"\n🎯 ISSUES IDENTIFIED:")
        print(f"   ❌ Only 1 epoch trained (needs 15-20 epochs)")
        print(f"   ❌ 76% accuracy is too low for medical use")
        print(f"   ❌ Model needs more training time")
        
        return True
    
    def create_enhanced_training(self):
        """Create enhanced training with better techniques"""
        
        print(f"\n🔧 CREATING ENHANCED TRAINING")
        print("=" * 50)
        
        # Load data
        preprocessor = DataPreprocessor()
        preprocessor.load_manifest()
        
        # Create data splits
        train_df, val_df, test_df = preprocessor.create_data_splits()
        
        print(f"📊 Dataset splits:")
        print(f"   Training: {len(train_df)} images")
        print(f"   Validation: {len(val_df)} images")
        print(f"   Testing: {len(test_df)} images")
        
        # Enhanced data loaders with better augmentation
        train_transforms, val_transforms = preprocessor.get_transforms()
        
        # Create datasets
        train_dataset = FootDeformityDataset(train_df, "processed_dataset", transform=train_transforms)
        val_dataset = FootDeformityDataset(val_df, "processed_dataset", transform=val_transforms)
        test_dataset = FootDeformityDataset(test_df, "processed_dataset", transform=val_transforms)
        
        # Create balanced sampler
        class_counts = train_df['class_id'].value_counts().sort_index()
        class_weights = compute_class_weight('balanced', classes=np.unique(train_df['class_id']), y=train_df['class_id'])
        sample_weights = [class_weights[class_id] for class_id in train_df['class_id']]
        sampler = WeightedRandomSampler(weights=sample_weights, num_samples=len(sample_weights), replacement=True)
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=16, sampler=sampler, num_workers=0, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False, num_workers=0, pin_memory=True)
        test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False, num_workers=0, pin_memory=True)
        
        print(f"✅ Enhanced data loaders created")
        
        return train_loader, val_loader, test_loader, class_weights
    
    def train_accurate_model(self, num_epochs=15):
        """Train model with enhanced techniques for better accuracy"""
        
        print(f"\n🎯 TRAINING ACCURATE MODEL")
        print("=" * 50)
        
        # Create enhanced data loaders
        train_loader, val_loader, test_loader, class_weights = self.create_enhanced_training()
        
        # Create fresh model
        model = create_model('resnet50', num_classes=4, pretrained=True)
        model.to(self.device)
        
        # Enhanced training setup
        class_weight_tensor = torch.FloatTensor(class_weights).to(self.device)
        criterion = nn.CrossEntropyLoss(weight=class_weight_tensor)
        
        # Use different learning rates for different parts
        backbone_params = []
        classifier_params = []
        
        for name, param in model.named_parameters():
            if 'fc' in name or 'classifier' in name:
                classifier_params.append(param)
            else:
                backbone_params.append(param)
        
        optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': 0.0001},  # Lower LR for pretrained backbone
            {'params': classifier_params, 'lr': 0.001}  # Higher LR for classifier
        ], weight_decay=1e-4)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=3, verbose=True)
        
        # Training history
        history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
        best_val_acc = 0.0
        best_model_state = None
        
        print(f"🚀 Starting enhanced training for {num_epochs} epochs...")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 30)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (images, labels) in enumerate(train_loader):
                images, labels = images.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, labels)
                loss.backward()
                
                # Gradient clipping for stability
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                if batch_idx % 50 == 0:
                    current_acc = 100. * train_correct / train_total
                    print(f"  Batch {batch_idx:3d} | Loss: {loss.item():.4f} | Acc: {current_acc:.1f}%")
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for images, labels in val_loader:
                    images, labels = images.to(self.device), labels.to(self.device)
                    
                    outputs = model(images)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels.size(0)
                    val_correct += (predicted == labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            # Calculate metrics
            train_loss_avg = train_loss / len(train_loader)
            train_acc = 100. * train_correct / train_total
            val_loss_avg = val_loss / len(val_loader)
            val_acc = 100. * val_correct / val_total
            
            # Update history
            history['train_loss'].append(train_loss_avg)
            history['train_acc'].append(train_acc)
            history['val_loss'].append(val_loss_avg)
            history['val_acc'].append(val_acc)
            
            # Print epoch summary
            print(f"\nEpoch {epoch+1} Summary:")
            print(f"  Train: Loss {train_loss_avg:.4f}, Acc {train_acc:.1f}%")
            print(f"  Val:   Loss {val_loss_avg:.4f}, Acc {val_acc:.1f}%")
            
            # Per-class validation accuracy
            print(f"  Per-class validation accuracy:")
            for i, class_name in enumerate(self.class_names):
                class_mask = np.array(all_labels) == i
                if class_mask.sum() > 0:
                    class_acc = (np.array(all_predictions)[class_mask] == np.array(all_labels)[class_mask]).mean() * 100
                    class_count = class_mask.sum()
                    print(f"    {class_name:15}: {class_acc:5.1f}% ({class_count} samples)")
            
            # Update learning rate
            scheduler.step(val_acc)
            
            # Save best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_model_state = model.state_dict().copy()
                
                # Save checkpoint
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_val_acc': best_val_acc,
                    'class_weights': class_weights,
                    'history': history
                }
                
                Path("models").mkdir(exist_ok=True)
                torch.save(checkpoint, "models/accurate_resnet50_model.pth")
                print(f"  💾 New best model saved! Val Acc: {val_acc:.1f}%")
            
            # Early stopping if accuracy is very high
            if val_acc > 95.0:
                print(f"🎉 Excellent accuracy achieved! Stopping early.")
                break
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        print(f"\n✅ Enhanced training completed!")
        print(f"🏆 Best validation accuracy: {best_val_acc:.1f}%")
        
        return model, history, test_loader
    
    def test_accurate_model(self, model, test_loader):
        """Test the accurate model"""
        
        print(f"\n📊 TESTING ACCURATE MODEL")
        print("=" * 50)
        
        model.eval()
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                
                outputs = model(images)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Calculate overall accuracy
        accuracy = (np.array(all_predictions) == np.array(all_labels)).mean() * 100
        print(f"🎯 Overall Test Accuracy: {accuracy:.1f}%")
        
        # Classification report
        print(f"\n📋 Detailed Classification Report:")
        report = classification_report(all_labels, all_predictions, target_names=self.class_names, digits=3)
        print(report)
        
        return accuracy

def main():
    """Main function to fix model accuracy"""
    
    print("🔧 FOOTAI ACCURACY FIX")
    print("=" * 60)
    
    fixer = AccuracyFixer()
    
    # Step 1: Diagnose current issues
    fixer.diagnose_current_model()
    
    # Step 2: Train accurate model
    model, history, test_loader = fixer.train_accurate_model(num_epochs=15)
    
    # Step 3: Test accurate model
    accuracy = fixer.test_accurate_model(model, test_loader)
    
    print(f"\n🎉 ACCURACY FIX RESULTS:")
    print(f"   🎯 Test Accuracy: {accuracy:.1f}%")
    print(f"   📈 Model saved as 'accurate_resnet50_model.pth'")
    
    if accuracy > 85:
        print(f"   ✅ Excellent accuracy achieved!")
    elif accuracy > 75:
        print(f"   ✅ Good accuracy achieved!")
    else:
        print(f"   ⚠️  Accuracy needs further improvement")
    
    print(f"\n🚀 Next steps:")
    print(f"   1. Update backend to use accurate_resnet50_model.pth")
    print(f"   2. Test with demo interface")
    print(f"   3. Verify improved predictions")

if __name__ == "__main__":
    main()
