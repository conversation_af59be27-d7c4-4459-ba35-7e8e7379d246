/**
 * lucide-react v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const FileLineChart = createLucideIcon("FileLineChart", [
  [
    "path",
    {
      d: "M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",
      key: "1nnpy2"
    }
  ],
  ["polyline", { points: "14 2 14 8 20 8", key: "1ew0cm" }],
  ["path", { d: "m16 13-3.5 3.5-2-2L8 17", key: "zz7yod" }]
]);

export { FileLineChart as default };
//# sourceMappingURL=file-line-chart.js.map
