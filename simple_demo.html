<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FootAI - Simple Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .hidden {
            display: none;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦶 FootAI - Simple Demo</h1>
            <p>AI-Powered Foot Deformity Classification</p>
        </div>

        <div id="backend-status" class="status offline">
            🔄 Checking backend connection...
        </div>

        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📁 Upload Foot Image</h3>
            <p>Click here to select an image file</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <div id="imagePreview" class="hidden">
            <h3>📸 Selected Image:</h3>
            <img id="previewImg" style="max-width: 100%; height: 200px; object-fit: contain; border: 1px solid #ddd; border-radius: 5px;">
            <br><br>
            <button onclick="analyzeImage()" id="analyzeBtn">🔍 Analyze Image</button>
            <button onclick="resetDemo()">🔄 Reset</button>
        </div>

        <div id="analysisProgress" class="hidden">
            <h3>⚡ Analyzing...</h3>
            <div class="progress">
                <div class="progress-bar" style="width: 0%"></div>
            </div>
        </div>

        <div id="results" class="results hidden">
            <h3>📊 Analysis Results</h3>
            <div id="resultsContent"></div>
        </div>

        <div class="header" style="margin-top: 40px;">
            <h3>🔧 System Information</h3>
            <p><strong>Backend:</strong> <span id="backendUrl">http://localhost:8000</span></p>
            <p><strong>Model:</strong> ResNet50 (96.9% accuracy)</p>
            <p><strong>Classes:</strong> Normal, Flatfoot, Foot Ulcer, Hallux Valgus</p>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // Check backend status
        async function checkBackend() {
            try {
                console.log('Checking backend status...');
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                document.getElementById('backend-status').innerHTML = '✅ Backend Online - Model Loaded';
                document.getElementById('backend-status').className = 'status online';
                
                console.log('Backend status:', data);
                return true;
            } catch (error) {
                console.error('Backend check failed:', error);
                document.getElementById('backend-status').innerHTML = '❌ Backend Offline - Please start the backend server';
                document.getElementById('backend-status').className = 'status offline';
                return false;
            }
        }

        // Handle file selection
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });

        // Analyze image
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }

            // Show progress
            document.getElementById('analysisProgress').classList.remove('hidden');
            document.getElementById('analyzeBtn').disabled = true;
            
            // Animate progress bar
            let progress = 0;
            const progressBar = document.querySelector('.progress-bar');
            const progressInterval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 100);

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                console.log('Sending image for analysis...');
                const response = await fetch('http://localhost:8000/predict', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('Analysis result:', result);
                
                // Complete progress
                progressBar.style.width = '100%';
                setTimeout(() => {
                    document.getElementById('analysisProgress').classList.add('hidden');
                    displayResults(result);
                }, 500);

            } catch (error) {
                console.error('Analysis failed:', error);
                alert('Analysis failed: ' + error.message);
                document.getElementById('analysisProgress').classList.add('hidden');
            } finally {
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        // Display results
        function displayResults(result) {
            const resultsContent = document.getElementById('resultsContent');
            
            const conditionColors = {
                'normal': '#28a745',
                'flatfoot': '#007bff',
                'foot_ulcer': '#dc3545',
                'hallux_valgus': '#6f42c1'
            };

            const html = `
                <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: ${conditionColors[result.predicted_class] || '#333'}; margin: 0 0 10px 0;">
                        🎯 Detected: ${result.predicted_class.replace('_', ' ').toUpperCase()}
                    </h4>
                    <p style="margin: 0 0 10px 0;"><strong>Confidence:</strong> ${(result.confidence * 100).toFixed(1)}%</p>
                    <p style="margin: 0;"><strong>Description:</strong> ${result.description}</p>
                </div>

                <h4>📈 Probability Breakdown:</h4>
                ${Object.entries(result.all_probabilities)
                    .sort(([,a], [,b]) => b - a)
                    .map(([condition, prob]) => `
                        <div style="margin: 10px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: bold; text-transform: capitalize;">
                                    ${condition.replace('_', ' ')}
                                </span>
                                <span>${(prob * 100).toFixed(1)}%</span>
                            </div>
                            <div style="background: #e9ecef; height: 10px; border-radius: 5px; overflow: hidden;">
                                <div style="background: ${conditionColors[condition] || '#6c757d'}; height: 100%; width: ${prob * 100}%; transition: width 0.5s ease;"></div>
                            </div>
                        </div>
                    `).join('')}

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <strong>⚠️ Medical Disclaimer:</strong> This AI analysis is for informational purposes only and should not replace professional medical diagnosis or treatment.
                </div>

                <div style="margin-top: 15px; font-size: 0.9em; color: #6c757d;">
                    <strong>Processing Time:</strong> ${(result.inference_time * 1000).toFixed(1)}ms |
                    <strong>Model:</strong> ${result.model_info?.architecture || 'ResNet50'} |
                    <strong>Device:</strong> ${result.model_info?.device || 'CPU'}
                </div>
            `;

            resultsContent.innerHTML = html;
            document.getElementById('results').classList.remove('hidden');
        }

        // Reset demo
        function resetDemo() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('imagePreview').classList.add('hidden');
            document.getElementById('results').classList.add('hidden');
            document.getElementById('analysisProgress').classList.add('hidden');
        }

        // Initialize
        window.addEventListener('load', function() {
            console.log('Demo loaded, checking backend...');
            checkBackend();
            
            // Check backend status every 30 seconds
            setInterval(checkBackend, 30000);
        });
    </script>
</body>
</html>
