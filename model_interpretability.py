"""
Model Interpretability Module
Implements Grad-CAM, SHAP, and attention visualization for ViT models
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import shap
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# Import our ViT model
from vit_model_architecture import FootDeformityViT, ViTWithGradCAM

class ModelInterpreter:
    """
    Comprehensive model interpretability for foot deformity classification
    """
    
    def __init__(self, model: torch.nn.Module, device: str = 'cpu'):
        self.model = model
        self.device = device
        self.class_names = ['Normal', 'Flatfoot', 'Foot Ulcer', 'Hallux Valgus']
        
        # Move model to device
        self.model.to(device)
        self.model.eval()
        
        print(f"🔍 Model Interpreter initialized on {device}")
    
    def generate_gradcam_heatmap(self, image: torch.Tensor, target_class: int = None, 
                                layer_name: str = None) -> np.ndarray:
        """
        Generate Grad-CAM heatmap for ViT model
        
        Args:
            image: Input image tensor [1, 3, 224, 224]
            target_class: Target class index (if None, uses predicted class)
            layer_name: Target layer name for Grad-CAM
            
        Returns:
            heatmap: Grad-CAM heatmap as numpy array
        """
        
        if isinstance(self.model, ViTWithGradCAM):
            # Use built-in Grad-CAM functionality
            gradcam = self.model.generate_gradcam(image, target_class)
            if gradcam is not None:
                return gradcam.cpu().numpy()[0]  # Return first batch item
        
        # Fallback: Manual Grad-CAM implementation for ViT
        return self._manual_gradcam_vit(image, target_class)
    
    def _manual_gradcam_vit(self, image: torch.Tensor, target_class: int = None) -> np.ndarray:
        """
        Manual Grad-CAM implementation for ViT
        """
        
        # Store gradients and activations
        gradients = []
        activations = []
        
        def backward_hook(module, grad_input, grad_output):
            gradients.append(grad_output[0])
        
        def forward_hook(module, input, output):
            activations.append(output)
        
        # Register hooks on the last transformer block
        target_layer = None
        for name, module in self.model.named_modules():
            if 'blocks' in name and ('norm2' in name or 'mlp' in name):
                target_layer = module
                break
        
        if target_layer is None:
            print("⚠️ Could not find suitable layer for Grad-CAM")
            return np.zeros((14, 14))
        
        # Register hooks
        forward_handle = target_layer.register_forward_hook(forward_hook)
        backward_handle = target_layer.register_backward_hook(backward_hook)
        
        try:
            # Forward pass
            image.requires_grad_(True)
            logits = self.model(image)
            
            if target_class is None:
                target_class = logits.argmax(dim=1).item()
            
            # Backward pass
            self.model.zero_grad()
            class_score = logits[0, target_class]
            class_score.backward()
            
            if gradients and activations:
                # Get gradients and activations
                grads = gradients[0]  # [batch_size, seq_len, embed_dim]
                acts = activations[0]  # [batch_size, seq_len, embed_dim]
                
                # Remove CLS token if present
                if grads.shape[1] > 196:  # 14*14 = 196 patches
                    grads = grads[:, 1:, :]
                    acts = acts[:, 1:, :]
                
                # Compute weights
                weights = grads.mean(dim=1, keepdim=True)  # [1, 1, embed_dim]
                
                # Weighted combination
                gradcam = (weights * acts).sum(dim=2)  # [1, num_patches]
                
                # Apply ReLU and normalize
                gradcam = F.relu(gradcam)
                gradcam = gradcam.view(14, 14)  # Reshape to spatial dimensions
                
                # Normalize
                gradcam = (gradcam - gradcam.min()) / (gradcam.max() - gradcam.min() + 1e-8)
                
                return gradcam.detach().cpu().numpy()
        
        finally:
            # Remove hooks
            forward_handle.remove()
            backward_handle.remove()
        
        return np.zeros((14, 14))
    
    def visualize_attention_maps(self, image: torch.Tensor, layer_idx: int = -1) -> np.ndarray:
        """
        Visualize attention maps from ViT
        
        Args:
            image: Input image tensor
            layer_idx: Transformer layer index to visualize
            
        Returns:
            attention_map: Attention visualization
        """
        
        if hasattr(self.model, 'get_attention_maps'):
            attention_maps = self.model.get_attention_maps(image, layer_idx)
            if attention_maps is not None:
                # Average across batch and reshape
                attention_map = attention_maps[0].cpu().numpy()  # [num_patches, num_patches]
                
                # Take attention to CLS token (first row) or average
                cls_attention = attention_map[0, :]  # Attention from CLS token
                
                # Reshape to spatial dimensions
                patch_size = int(np.sqrt(len(cls_attention)))
                attention_spatial = cls_attention.reshape(patch_size, patch_size)
                
                # Normalize
                attention_spatial = (attention_spatial - attention_spatial.min()) / \
                                  (attention_spatial.max() - attention_spatial.min() + 1e-8)
                
                return attention_spatial
        
        return np.zeros((14, 14))
    
    def create_shap_explainer(self, background_data: torch.Tensor, num_samples: int = 100):
        """
        Create SHAP explainer for the model
        
        Args:
            background_data: Background dataset for SHAP
            num_samples: Number of background samples to use
        """
        
        # Sample background data
        if len(background_data) > num_samples:
            indices = np.random.choice(len(background_data), num_samples, replace=False)
            background = background_data[indices]
        else:
            background = background_data
        
        # Create SHAP explainer
        def model_wrapper(x):
            x_tensor = torch.from_numpy(x).float().to(self.device)
            with torch.no_grad():
                outputs = self.model(x_tensor)
                return F.softmax(outputs, dim=1).cpu().numpy()
        
        self.explainer = shap.DeepExplainer(model_wrapper, background.numpy())
        print(f"✅ SHAP explainer created with {len(background)} background samples")
    
    def generate_shap_explanation(self, image: torch.Tensor, class_idx: int = None) -> np.ndarray:
        """
        Generate SHAP explanation for an image
        
        Args:
            image: Input image tensor
            class_idx: Target class index
            
        Returns:
            shap_values: SHAP values for the image
        """
        
        if not hasattr(self, 'explainer'):
            print("❌ SHAP explainer not initialized. Call create_shap_explainer first.")
            return None
        
        # Get SHAP values
        shap_values = self.explainer.shap_values(image.cpu().numpy())
        
        if class_idx is not None:
            return shap_values[class_idx]
        else:
            # Return SHAP values for predicted class
            with torch.no_grad():
                logits = self.model(image)
                predicted_class = logits.argmax(dim=1).item()
            return shap_values[predicted_class]
    
    def create_comprehensive_visualization(self, image_path: str, save_path: str = None) -> Dict:
        """
        Create comprehensive visualization with all interpretability methods
        
        Args:
            image_path: Path to input image
            save_path: Path to save visualization
            
        Returns:
            results: Dictionary containing all interpretability results
        """
        
        # Load and preprocess image
        image = Image.open(image_path).convert('RGB')
        image_np = np.array(image)
        
        # Preprocess for model
        from torchvision import transforms
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        image_tensor = transform(image).unsqueeze(0).to(self.device)
        
        # Get prediction
        with torch.no_grad():
            logits = self.model(image_tensor)
            probabilities = F.softmax(logits, dim=1)
            predicted_class = logits.argmax(dim=1).item()
            confidence = probabilities[0, predicted_class].item()
        
        # Generate interpretability visualizations
        gradcam_heatmap = self.generate_gradcam_heatmap(image_tensor, predicted_class)
        attention_map = self.visualize_attention_maps(image_tensor)
        
        # Create visualization
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'Foot Deformity Classification Interpretability\n'
                    f'Predicted: {self.class_names[predicted_class]} ({confidence:.1%})', 
                    fontsize=16, fontweight='bold')
        
        # Original image
        axes[0, 0].imshow(image_np)
        axes[0, 0].set_title('Original Image', fontweight='bold')
        axes[0, 0].axis('off')
        
        # Grad-CAM overlay
        gradcam_resized = cv2.resize(gradcam_heatmap, (224, 224))
        axes[0, 1].imshow(image_np)
        axes[0, 1].imshow(gradcam_resized, alpha=0.6, cmap='jet')
        axes[0, 1].set_title('Grad-CAM Overlay', fontweight='bold')
        axes[0, 1].axis('off')
        
        # Grad-CAM heatmap
        im1 = axes[0, 2].imshow(gradcam_heatmap, cmap='jet')
        axes[0, 2].set_title('Grad-CAM Heatmap', fontweight='bold')
        axes[0, 2].axis('off')
        plt.colorbar(im1, ax=axes[0, 2], fraction=0.046, pad=0.04)
        
        # Attention map
        im2 = axes[1, 0].imshow(attention_map, cmap='viridis')
        axes[1, 0].set_title('Attention Map', fontweight='bold')
        axes[1, 0].axis('off')
        plt.colorbar(im2, ax=axes[1, 0], fraction=0.046, pad=0.04)
        
        # Prediction probabilities
        probs = probabilities[0].cpu().numpy()
        bars = axes[1, 1].bar(self.class_names, probs, color=['green', 'blue', 'red', 'purple'])
        axes[1, 1].set_title('Class Probabilities', fontweight='bold')
        axes[1, 1].set_ylabel('Probability')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # Highlight predicted class
        bars[predicted_class].set_color('orange')
        
        # Add probability values on bars
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{prob:.1%}', ha='center', va='bottom', fontweight='bold')
        
        # Medical interpretation
        axes[1, 2].axis('off')
        interpretation_text = self._generate_medical_interpretation(predicted_class, confidence, probs)
        axes[1, 2].text(0.1, 0.9, interpretation_text, transform=axes[1, 2].transAxes,
                       fontsize=10, verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", 
                       facecolor="lightblue", alpha=0.7))
        axes[1, 2].set_title('Medical Interpretation', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"💾 Visualization saved to {save_path}")
        
        plt.show()
        
        # Return results
        results = {
            'predicted_class': self.class_names[predicted_class],
            'predicted_class_idx': predicted_class,
            'confidence': confidence,
            'probabilities': probs,
            'gradcam_heatmap': gradcam_heatmap,
            'attention_map': attention_map,
            'interpretation': interpretation_text
        }
        
        return results
    
    def _generate_medical_interpretation(self, predicted_class: int, confidence: float, 
                                       probabilities: np.ndarray) -> str:
        """Generate medical interpretation of the results"""
        
        class_name = self.class_names[predicted_class]
        
        interpretation = f"DIAGNOSIS: {class_name}\n"
        interpretation += f"Confidence: {confidence:.1%}\n\n"
        
        if predicted_class == 0:  # Normal
            interpretation += "✅ No significant deformity detected.\n"
            interpretation += "Recommendation: Routine monitoring."
        elif predicted_class == 1:  # Flatfoot
            interpretation += "⚠️ Flatfoot (Pes Planus) detected.\n"
            interpretation += "Recommendation: Orthotic evaluation,\nphysical therapy consultation."
        elif predicted_class == 2:  # Foot Ulcer
            interpretation += "🚨 Foot ulcer detected.\n"
            interpretation += "Recommendation: Immediate medical\nattention, wound care specialist."
        elif predicted_class == 3:  # Hallux Valgus
            interpretation += "⚠️ Hallux Valgus (Bunion) detected.\n"
            interpretation += "Recommendation: Orthopedic consultation,\nproper footwear assessment."
        
        # Add confidence interpretation
        if confidence > 0.8:
            interpretation += f"\n\nHigh confidence prediction.\nModel is very certain about diagnosis."
        elif confidence > 0.6:
            interpretation += f"\n\nModerate confidence prediction.\nConsider additional imaging if needed."
        else:
            interpretation += f"\n\nLow confidence prediction.\nRecommend clinical evaluation\nand additional tests."
        
        return interpretation

def test_interpretability():
    """Test the interpretability module"""
    
    print("🔍 Testing Model Interpretability")
    print("=" * 40)
    
    # Create a dummy ViT model
    from vit_model_architecture import create_vit_model
    
    model = create_vit_model(num_classes=4, model_type="gradcam", pretrained=False)
    interpreter = ModelInterpreter(model, device='cpu')
    
    # Create dummy input
    dummy_image = torch.randn(1, 3, 224, 224)
    
    # Test Grad-CAM
    gradcam = interpreter.generate_gradcam_heatmap(dummy_image)
    print(f"✅ Grad-CAM generated: {gradcam.shape}")
    
    # Test attention maps
    attention = interpreter.visualize_attention_maps(dummy_image)
    print(f"✅ Attention map generated: {attention.shape}")
    
    print("✅ Interpretability module test completed!")

if __name__ == "__main__":
    test_interpretability()
