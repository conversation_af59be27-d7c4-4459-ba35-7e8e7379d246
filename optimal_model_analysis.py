"""
Optimal Model Analysis for Dataset-Specific Best Accuracy
Analyze dataset characteristics and recommend best model for internal/external validation
"""

import pandas as pd
import numpy as np
from pathlib import Path
import torch
import matplotlib.pyplot as plt
from collections import Counter

def analyze_dataset_characteristics():
    """Analyze dataset to determine optimal model choice"""
    
    print("📊 DATASET ANALYSIS FOR OPTIMAL MODEL SELECTION")
    print("=" * 60)
    
    # Load dataset manifest
    manifest_path = Path("processed_dataset/dataset_manifest.csv")
    if not manifest_path.exists():
        print("❌ Dataset manifest not found")
        return None
    
    df = pd.read_csv(manifest_path)
    
    print(f"📈 DATASET CHARACTERISTICS:")
    print(f"   Total Images: {len(df):,}")
    print(f"   Classes: {df['class_name'].nunique()}")
    print(f"   Image Sources: {df['source'].nunique() if 'source' in df.columns else 'Unknown'}")
    
    # Class distribution
    class_dist = df['class_name'].value_counts()
    print(f"\n📊 CLASS DISTRIBUTION:")
    for class_name, count in class_dist.items():
        percentage = (count / len(df)) * 100
        print(f"   {class_name:15}: {count:4d} images ({percentage:5.1f}%)")
    
    # Calculate imbalance ratio
    max_class = class_dist.max()
    min_class = class_dist.min()
    imbalance_ratio = max_class / min_class
    print(f"\n⚖️ CLASS IMBALANCE RATIO: {imbalance_ratio:.1f}:1")
    
    return df, class_dist, imbalance_ratio

def evaluate_model_performance():
    """Evaluate current model performance"""
    
    print(f"\n🎯 MODEL PERFORMANCE EVALUATION:")
    print("-" * 40)
    
    models_dir = Path("models")
    model_results = {}
    
    # Check ViT model
    vit_path = models_dir / "vit_simple_best.pth"
    if vit_path.exists():
        try:
            checkpoint = torch.load(vit_path, map_location='cpu')
            vit_acc = checkpoint.get('best_val_acc', 0)
            model_results['ViT'] = {
                'accuracy': vit_acc,
                'parameters': '5.2M',
                'interpretability': 'High',
                'training': 'From scratch'
            }
            print(f"✅ ViT Model: {vit_acc:.1f}% validation accuracy")
        except Exception as e:
            print(f"❌ ViT Model error: {e}")
    
    # Check CNN models
    cnn_models = [
        ("ResNet50", "best_resnet50_model.pth"),
        ("Improved ResNet50", "improved_resnet50_model.pth")
    ]
    
    for model_name, filename in cnn_models:
        model_path = models_dir / filename
        if model_path.exists():
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
                acc = checkpoint.get('best_val_acc', 0)
                model_results[model_name] = {
                    'accuracy': acc,
                    'parameters': '24.7M',
                    'interpretability': 'Medium',
                    'training': 'Pre-trained + Fine-tuned'
                }
                print(f"✅ {model_name}: {acc:.1f}% validation accuracy")
            except Exception as e:
                print(f"❌ {model_name} error: {e}")
    
    return model_results

def recommend_optimal_model(df, class_dist, imbalance_ratio, model_results):
    """Recommend optimal model based on dataset characteristics"""
    
    print(f"\n🧠 OPTIMAL MODEL RECOMMENDATION:")
    print("=" * 50)
    
    # Dataset characteristics analysis
    total_images = len(df)
    num_classes = df['class_name'].nunique()
    
    print(f"📊 DATASET ANALYSIS RESULTS:")
    print(f"   Dataset Size: {total_images:,} images")
    print(f"   Number of Classes: {num_classes}")
    print(f"   Class Imbalance: {imbalance_ratio:.1f}:1")
    
    # Determine dataset size category
    if total_images < 1000:
        size_category = "Small"
    elif total_images < 5000:
        size_category = "Medium"
    elif total_images < 20000:
        size_category = "Large"
    else:
        size_category = "Very Large"
    
    print(f"   Size Category: {size_category}")
    
    # Determine imbalance severity
    if imbalance_ratio < 2:
        imbalance_severity = "Low"
    elif imbalance_ratio < 5:
        imbalance_severity = "Moderate"
    elif imbalance_ratio < 10:
        imbalance_severity = "High"
    else:
        imbalance_severity = "Severe"
    
    print(f"   Imbalance Severity: {imbalance_severity}")
    
    # Model recommendation logic
    print(f"\n🎯 RECOMMENDATION ANALYSIS:")
    
    recommendations = []
    
    # For medical imaging with moderate dataset size
    if size_category in ["Medium", "Large"] and total_images > 3000:
        recommendations.append({
            'model': 'Pre-trained CNN (ResNet50)',
            'reason': 'Sufficient data for fine-tuning, proven medical imaging performance',
            'priority': 1
        })
    
    # For interpretability requirements
    if any('interpretability' in str(model_results.get(model, {})) for model in model_results):
        recommendations.append({
            'model': 'Ensemble (CNN + ViT)',
            'reason': 'Best accuracy + interpretability combination',
            'priority': 2
        })
    
    # For class imbalance
    if imbalance_severity in ["High", "Severe"]:
        recommendations.append({
            'model': 'Weighted CNN with Data Augmentation',
            'reason': 'Better handling of imbalanced classes',
            'priority': 3
        })
    
    # Based on current performance
    best_model = max(model_results.items(), key=lambda x: x[1]['accuracy'])
    recommendations.append({
        'model': best_model[0],
        'reason': f'Highest current accuracy: {best_model[1]["accuracy"]:.1f}%',
        'priority': 4
    })
    
    return recommendations

def create_optimal_training_strategy(recommendations, df):
    """Create optimal training strategy"""
    
    print(f"\n🚀 OPTIMAL TRAINING STRATEGY:")
    print("=" * 50)
    
    # Based on dataset size and characteristics
    total_images = len(df)
    
    strategy = {
        'primary_model': 'ResNet50',
        'backup_model': 'ViT',
        'training_approach': 'Transfer Learning',
        'data_augmentation': 'Heavy',
        'class_balancing': 'Weighted Sampling',
        'validation_strategy': 'Stratified K-Fold',
        'external_validation': 'Domain Adaptation'
    }
    
    print(f"🎯 RECOMMENDED STRATEGY:")
    print(f"   Primary Model: {strategy['primary_model']}")
    print(f"   Backup Model: {strategy['backup_model']}")
    print(f"   Training: {strategy['training_approach']}")
    print(f"   Augmentation: {strategy['data_augmentation']}")
    print(f"   Class Balance: {strategy['class_balancing']}")
    print(f"   Validation: {strategy['validation_strategy']}")
    print(f"   External Val: {strategy['external_validation']}")
    
    # Specific recommendations
    print(f"\n📋 SPECIFIC RECOMMENDATIONS:")
    
    if total_images > 5000:
        print(f"✅ Use ResNet50 with ImageNet pre-training")
        print(f"✅ Fine-tune all layers with low learning rate")
        print(f"✅ Heavy data augmentation for generalization")
    else:
        print(f"⚠️ Consider data collection or synthetic augmentation")
        print(f"✅ Use pre-trained features with frozen backbone")
    
    print(f"✅ Implement class-weighted loss function")
    print(f"✅ Use stratified train/val/test splits")
    print(f"✅ Apply external validation with domain shift testing")
    print(f"✅ Ensemble multiple models for production")
    
    return strategy

def main():
    """Main analysis function"""
    
    print("🔍 OPTIMAL MODEL ANALYSIS FOR FOOT DEFORMITY CLASSIFICATION")
    print("=" * 70)
    
    # Step 1: Analyze dataset
    result = analyze_dataset_characteristics()
    if result is None:
        return
    
    df, class_dist, imbalance_ratio = result
    
    # Step 2: Evaluate current models
    model_results = evaluate_model_performance()
    
    # Step 3: Get recommendations
    recommendations = recommend_optimal_model(df, class_dist, imbalance_ratio, model_results)
    
    # Step 4: Create training strategy
    strategy = create_optimal_training_strategy(recommendations, df)
    
    # Final recommendation
    print(f"\n🏆 FINAL RECOMMENDATION:")
    print("=" * 50)
    
    if model_results:
        best_current = max(model_results.items(), key=lambda x: x[1]['accuracy'])
        print(f"🥇 BEST CURRENT MODEL: {best_current[0]}")
        print(f"   Accuracy: {best_current[1]['accuracy']:.1f}%")
        print(f"   Parameters: {best_current[1]['parameters']}")
        print(f"   Interpretability: {best_current[1]['interpretability']}")
    
    print(f"\n🎯 FOR PRODUCTION DEPLOYMENT:")
    print(f"   ✅ Use ResNet50 for highest accuracy")
    print(f"   ✅ Implement ensemble with ViT for interpretability")
    print(f"   ✅ Apply domain adaptation for external validation")
    print(f"   ✅ Use conservative confidence thresholds")
    
    print(f"\n🔬 FOR RESEARCH & DEVELOPMENT:")
    print(f"   ✅ Continue ViT development with more data")
    print(f"   ✅ Explore hybrid CNN-Transformer architectures")
    print(f"   ✅ Implement advanced interpretability methods")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")

if __name__ == "__main__":
    main()
