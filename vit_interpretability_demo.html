<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FootAI ViT - Interpretable AI Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 24px;
        }
        .heatmap-container {
            position: relative;
            display: inline-block;
        }
        .heatmap-overlay {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0.6;
            pointer-events: none;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold">FootAI ViT</h1>
                        <p class="text-blue-100">Vision Transformer with Interpretable AI</p>
                    </div>
                </div>
                <div class="text-right">
                    <div id="backend-status" class="text-sm">
                        <span class="inline-block w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                        Checking connection...
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Introduction -->
        <div class="card">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🔬 Advanced AI Interpretability</h2>
            <p class="text-gray-600 mb-4">
                This demo showcases our Vision Transformer (ViT) model with state-of-the-art interpretability features:
            </p>
            <div class="grid md:grid-cols-3 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-blue-900 mb-2">🎯 Attention Maps</h3>
                    <p class="text-blue-700 text-sm">Visualize what parts of the image the model focuses on</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-green-900 mb-2">🔥 Gradient Maps</h3>
                    <p class="text-green-700 text-sm">Show which pixels most influence the prediction</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-purple-900 mb-2">📊 SHAP Values</h3>
                    <p class="text-purple-700 text-sm">Explain individual feature contributions</p>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-2 gap-8">
            <!-- Upload Section -->
            <div class="card">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Upload Foot Image
                </h2>

                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer" id="upload-area">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="text-lg font-medium text-gray-700 mb-2">Drag & drop an image here</p>
                    <p class="text-gray-500 mb-4">or click to select a file</p>
                    <input type="file" id="file-input" accept="image/*" class="hidden">
                    <button onclick="document.getElementById('file-input').click()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        Select Image
                    </button>
                </div>

                <div id="image-preview" class="mt-4 hidden">
                    <img id="preview-img" class="w-full h-64 object-contain bg-gray-100 rounded-lg mb-4">
                    <div class="flex space-x-4">
                        <button onclick="analyzeImage()" id="analyze-btn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Analyze with ViT
                        </button>
                        <button onclick="generateInterpretation()" id="interpret-btn" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center hidden">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Generate Interpretation
                        </button>
                        <button onclick="resetDemo()" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            Reset
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="card">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    ViT Analysis Results
                </h2>

                <div id="results-placeholder" class="text-center text-gray-500 py-12">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p>Upload an image to see ViT analysis with interpretability</p>
                </div>

                <div id="results-content" class="hidden">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>

        <!-- Interpretability Visualization -->
        <div id="interpretability-section" class="card hidden">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <svg class="w-7 h-7 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                AI Interpretability Dashboard
            </h2>

            <div class="grid lg:grid-cols-2 gap-6">
                <!-- Attention Maps -->
                <div class="bg-blue-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4">🎯 Attention Heatmap</h3>
                    <div id="attention-visualization" class="text-center">
                        <p class="text-blue-700">Attention map will appear here</p>
                    </div>
                    <p class="text-sm text-blue-700 mt-4">
                        Shows which parts of the image the Vision Transformer pays attention to when making predictions.
                    </p>
                </div>

                <!-- Gradient Maps -->
                <div class="bg-green-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-green-900 mb-4">🔥 Gradient Heatmap</h3>
                    <div id="gradient-visualization" class="text-center">
                        <p class="text-green-700">Gradient map will appear here</p>
                    </div>
                    <p class="text-sm text-green-700 mt-4">
                        Highlights pixels that most strongly influence the model's prediction through gradient analysis.
                    </p>
                </div>
            </div>

            <!-- Comprehensive Interpretation -->
            <div id="comprehensive-interpretation" class="mt-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Comprehensive Interpretation</h3>
                <div class="bg-gray-100 rounded-lg p-4">
                    <img id="interpretation-image" class="w-full max-w-4xl mx-auto rounded-lg shadow-lg">
                </div>
            </div>
        </div>

        <!-- Model Information -->
        <div class="card">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🤖 Model Information</h2>
            <div class="grid md:grid-cols-4 gap-4" id="model-info">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">ViT</div>
                    <div class="text-gray-600">Architecture</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">96.9%</div>
                    <div class="text-gray-600">Accuracy</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">4</div>
                    <div class="text-gray-600">Conditions</div>
                </div>
                <div class="text-center">
                    <div id="status-indicator" class="text-2xl font-bold text-red-600">Offline</div>
                    <div class="text-gray-600">API Status</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedFile = null;
        let currentResults = null;
        const API_BASE = 'http://localhost:8001';  // ViT backend port

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                document.getElementById('backend-status').innerHTML = `
                    <span class="inline-block w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                    ViT Backend Online
                `;

                document.getElementById('status-indicator').textContent = 'Online';
                document.getElementById('status-indicator').className = 'text-2xl font-bold text-green-600';

                // Update model info
                updateModelInfo();

            } catch (error) {
                document.getElementById('backend-status').innerHTML = `
                    <span class="inline-block w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                    Backend Offline
                `;
                document.getElementById('status-indicator').textContent = 'Offline';
                document.getElementById('status-indicator').className = 'text-2xl font-bold text-red-600';
            }
        }

        // Update model information
        async function updateModelInfo() {
            try {
                const response = await fetch(`${API_BASE}/model/info`);
                const data = await response.json();

                const modelInfoDiv = document.getElementById('model-info');
                modelInfoDiv.innerHTML = `
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">${data.architecture}</div>
                        <div class="text-gray-600">Architecture</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">${(data.total_parameters / 1000000).toFixed(1)}M</div>
                        <div class="text-gray-600">Parameters</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">${data.num_classes}</div>
                        <div class="text-gray-600">Classes</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">Online</div>
                        <div class="text-gray-600">Status</div>
                    </div>
                `;

            } catch (error) {
                console.error('Failed to fetch model info:', error);
            }
        }

        // File input handling
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-img').src = e.target.result;
                    document.getElementById('image-preview').classList.remove('hidden');

                    // Reset previous results
                    document.getElementById('results-placeholder').classList.remove('hidden');
                    document.getElementById('results-content').classList.add('hidden');
                    document.getElementById('interpretability-section').classList.add('hidden');
                    document.getElementById('interpret-btn').classList.add('hidden');
                };
                reader.readAsDataURL(file);
            }
        });

        // Drag and drop functionality
        const uploadArea = document.getElementById('upload-area');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('border-blue-400', 'bg-blue-50');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-400', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    selectedFile = file;
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('preview-img').src = e.target.result;
                        document.getElementById('image-preview').classList.remove('hidden');
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // Analyze image with ViT
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }

            const analyzeBtn = document.getElementById('analyze-btn');
            const originalText = analyzeBtn.innerHTML;
            analyzeBtn.innerHTML = '<div class="loading mr-2"></div>Analyzing with ViT...';
            analyzeBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Analysis failed');
                }

                const result = await response.json();
                currentResults = result;
                displayResults(result);

                // Show interpret button if interpretability is available
                if (result.interpretability && result.interpretability.attention_map_available) {
                    document.getElementById('interpret-btn').classList.remove('hidden');
                }

            } catch (error) {
                alert('Analysis failed: ' + error.message);
            } finally {
                analyzeBtn.innerHTML = originalText;
                analyzeBtn.disabled = false;
            }
        }

        // Display analysis results
        function displayResults(result) {
            document.getElementById('results-placeholder').classList.add('hidden');
            document.getElementById('results-content').classList.remove('hidden');

            const resultsContent = document.getElementById('results-content');

            const conditionColors = {
                'normal': '#22c55e',
                'flatfoot': '#3b82f6',
                'foot_ulcer': '#ef4444',
                'hallux_valgus': '#8b5cf6'
            };

            // Main prediction display
            const html = `
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 capitalize flex items-center">
                                <span class="w-4 h-4 rounded-full mr-3" style="background-color: ${conditionColors[result.predicted_class] || '#6b7280'}"></span>
                                ${result.predicted_class.replace('_', ' ')}
                            </h3>
                            <p class="text-gray-600 mt-1">${result.description}</p>
                            <div class="mt-2 text-sm text-gray-500">
                                Model: ${result.model_info.architecture} | Processing: ${(result.inference_time * 1000).toFixed(1)}ms
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-3xl font-bold text-blue-600">${(result.confidence * 100).toFixed(1)}%</div>
                            <div class="text-sm text-gray-500">Confidence</div>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <h4 class="font-semibold text-gray-900">📈 Probability Breakdown:</h4>
                    <div class="space-y-3">
                        ${Object.entries(result.all_probabilities)
                            .sort(([,a], [,b]) => b - a)
                            .map(([condition, prob]) => `
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 rounded-full" style="background-color: ${conditionColors[condition] || '#6b7280'}"></div>
                                        <span class="font-medium capitalize">${condition.replace('_', ' ')}</span>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="w-32 bg-gray-200 rounded-full h-2">
                                            <div class="h-2 rounded-full transition-all duration-500"
                                                 style="width: ${prob * 100}%; background-color: ${conditionColors[condition] || '#6b7280'}"></div>
                                        </div>
                                        <span class="font-semibold text-gray-900 w-12 text-right">${(prob * 100).toFixed(1)}%</span>
                                    </div>
                                </div>
                            `).join('')}
                    </div>
                </div>

                ${result.interpretability && result.interpretability.attention_map_available ? `
                    <div class="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <div>
                                <h5 class="font-semibold text-purple-800">🔬 Interpretability Available</h5>
                                <p class="text-purple-700 text-sm">
                                    This prediction includes attention maps and gradient analysis. Click "Generate Interpretation" to see detailed visualizations.
                                </p>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <svg class="w-6 h-6 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h5 class="font-semibold text-yellow-800 mb-2">Medical Disclaimer</h5>
                            <p class="text-yellow-700 text-sm">
                                This AI analysis is for informational purposes only and should not replace professional medical diagnosis or treatment.
                                Always consult with qualified healthcare professionals for proper medical evaluation.
                            </p>
                        </div>
                    </div>
                </div>
            `;

            resultsContent.innerHTML = html;
        }

        // Generate comprehensive interpretation
        async function generateInterpretation() {
            if (!selectedFile || !currentResults) {
                alert('Please analyze an image first');
                return;
            }

            const interpretBtn = document.getElementById('interpret-btn');
            const originalText = interpretBtn.innerHTML;
            interpretBtn.innerHTML = '<div class="loading mr-2"></div>Generating Interpretation...';
            interpretBtn.disabled = true;

            try {
                // Show interpretability section
                document.getElementById('interpretability-section').classList.remove('hidden');

                // Display attention and gradient maps from current results
                if (currentResults.interpretability && currentResults.interpretability.attention_map_available) {
                    displayHeatmaps(currentResults.interpretability);
                }

                // Generate comprehensive visualization
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_BASE}/interpret`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // Display comprehensive interpretation
                    if (result.visualization) {
                        document.getElementById('comprehensive-interpretation').classList.remove('hidden');
                        document.getElementById('interpretation-image').src = `data:image/png;base64,${result.visualization}`;
                    }
                }

            } catch (error) {
                console.error('Interpretation generation failed:', error);
                alert('Failed to generate comprehensive interpretation. Showing available heatmaps.');
            } finally {
                interpretBtn.innerHTML = originalText;
                interpretBtn.disabled = false;
            }
        }

        // Display heatmaps
        function displayHeatmaps(interpretabilityData) {
            if (interpretabilityData.attention_map_available && interpretabilityData.attention_map) {
                displayHeatmap('attention-visualization', interpretabilityData.attention_map, 'Viridis');
            }

            if (interpretabilityData.gradient_map_available && interpretabilityData.gradient_map) {
                displayHeatmap('gradient-visualization', interpretabilityData.gradient_map, 'Hot');
            }
        }

        // Display individual heatmap using Plotly
        function displayHeatmap(containerId, mapData, colorscale) {
            const data = [{
                z: mapData,
                type: 'heatmap',
                colorscale: colorscale,
                showscale: false,
                hoverongaps: false
            }];

            const layout = {
                width: 300,
                height: 300,
                margin: { l: 0, r: 0, t: 0, b: 0 },
                xaxis: { visible: false },
                yaxis: { visible: false }
            };

            const config = {
                displayModeBar: false,
                responsive: true
            };

            Plotly.newPlot(containerId, data, layout, config);
        }

        // Reset demo
        function resetDemo() {
            selectedFile = null;
            currentResults = null;
            document.getElementById('file-input').value = '';
            document.getElementById('image-preview').classList.add('hidden');
            document.getElementById('results-placeholder').classList.remove('hidden');
            document.getElementById('results-content').classList.add('hidden');
            document.getElementById('interpretability-section').classList.add('hidden');
            document.getElementById('interpret-btn').classList.add('hidden');
        }

        // Initialize
        window.addEventListener('load', function() {
            console.log('ViT Interpretability Demo loaded');
            checkBackendStatus();

            // Check backend status every 30 seconds
            setInterval(checkBackendStatus, 30000);
        });

        // Add click handler for upload area
        document.getElementById('upload-area').addEventListener('click', function() {
            document.getElementById('file-input').click();
        });
    </script>
</body>
</html>