"""
Improved MONAI Training with Robust External Validation
Enhanced training with better data handling and validation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import time
from PIL import Image
import torchvision.transforms as transforms

# <PERSON><PERSON><PERSON>I imports
import monai
from monai.networks.nets import DenseNet121
from monai.utils import set_determinism

class ImprovedMONAITrainer:
    """Improved MONAI trainer with robust validation"""
    
    def __init__(self, num_classes=4):
        self.num_classes = num_classes
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        
        # Set determinism for reproducibility
        set_determinism(seed=42)
        
        print("🏥 IMPROVED MONAI MEDICAL TRAINER")
        print("=" * 50)
        print(f"Device: {self.device}")
        print(f"MONAI Version: {monai.__version__}")
    
    def robust_image_loader(self, image_path):
        """Robust image loading with multiple fallback methods"""
        
        try:
            # Method 1: Standard RGB conversion
            image = Image.open(image_path).convert('RGB')
            return image
        except Exception:
            try:
                # Method 2: RGBA to RGB with white background
                img_rgba = Image.open(image_path).convert('RGBA')
                background = Image.new('RGB', img_rgba.size, (255, 255, 255))
                background.paste(img_rgba, mask=img_rgba.split()[-1])
                return background
            except Exception:
                try:
                    # Method 3: Grayscale to RGB
                    img_gray = Image.open(image_path).convert('L')
                    return Image.merge('RGB', (img_gray, img_gray, img_gray))
                except Exception as e:
                    print(f"⚠️ Failed to load {image_path}: {e}")
                    return None
    
    def create_robust_transforms(self):
        """Create robust transforms with data augmentation"""
        
        # Training transforms with augmentation
        train_transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Validation transforms (no augmentation)
        val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return train_transform, val_transform
    
    def create_robust_dataset(self, data_list, transform, is_training=True):
        """Create robust dataset with error handling"""
        
        class RobustDataset(torch.utils.data.Dataset):
            def __init__(self, data_list, transform, image_loader, is_training=True):
                self.data_list = data_list
                self.transform = transform
                self.image_loader = image_loader
                self.is_training = is_training
                
                # Pre-validate images
                self.valid_indices = []
                print(f"🔍 Validating {len(data_list)} images...")
                
                for i, data in enumerate(data_list):
                    image_path = Path(data['image'])
                    if image_path.exists():
                        # Try to load image
                        image = self.image_loader(image_path)
                        if image is not None:
                            self.valid_indices.append(i)
                        else:
                            print(f"⚠️ Skipping invalid image: {image_path}")
                    else:
                        print(f"⚠️ Image not found: {image_path}")
                
                print(f"✅ Valid images: {len(self.valid_indices)}/{len(data_list)}")
            
            def __len__(self):
                return len(self.valid_indices)
            
            def __getitem__(self, idx):
                actual_idx = self.valid_indices[idx]
                data = self.data_list[actual_idx]
                
                # Load image
                image = self.image_loader(data['image'])
                if image is None:
                    # Return a dummy image if loading fails
                    image = Image.new('RGB', (224, 224), color=(128, 128, 128))
                
                # Apply transforms
                try:
                    image_tensor = self.transform(image)
                except Exception as e:
                    print(f"⚠️ Transform failed: {e}")
                    # Create dummy tensor
                    image_tensor = torch.zeros(3, 224, 224)
                
                # Handle NaN values
                if torch.isnan(image_tensor).any():
                    image_tensor = torch.nan_to_num(image_tensor, nan=0.0)
                
                return {
                    "image": image_tensor,
                    "label": torch.tensor(data['label'], dtype=torch.long)
                }
        
        return RobustDataset(data_list, transform, self.robust_image_loader, is_training)
    
    def prepare_data(self):
        """Prepare training and validation data"""
        
        print("📊 Preparing robust dataset...")
        
        # Load dataset manifest
        manifest_path = Path("processed_dataset/dataset_manifest.csv")
        if not manifest_path.exists():
            raise FileNotFoundError("Dataset manifest not found")
        
        df = pd.read_csv(manifest_path)
        print(f"Total images in manifest: {len(df)}")
        
        # Verify image existence
        valid_rows = []
        for _, row in df.iterrows():
            image_path = Path("processed_dataset") / row['class'] / row['filename']
            if image_path.exists():
                valid_rows.append(row)
            else:
                print(f"⚠️ Missing image: {image_path}")
        
        if not valid_rows:
            raise ValueError("No valid images found")
        
        valid_df = pd.DataFrame(valid_rows)
        print(f"Valid images found: {len(valid_df)}")
        
        # Stratified split
        train_df, temp_df = train_test_split(
            valid_df, test_size=0.3, stratify=valid_df['class_id'], random_state=42
        )
        val_df, test_df = train_test_split(
            temp_df, test_size=0.5, stratify=temp_df['class_id'], random_state=42
        )
        
        print(f"Training: {len(train_df)} images")
        print(f"Validation: {len(val_df)} images")
        print(f"Test: {len(test_df)} images")
        
        # Create data dictionaries
        def create_data_dicts(dataframe):
            data_dicts = []
            for _, row in dataframe.iterrows():
                image_path = Path("processed_dataset") / row['class'] / row['filename']
                data_dicts.append({
                    "image": str(image_path),
                    "label": row['class_id']
                })
            return data_dicts
        
        train_data = create_data_dicts(train_df)
        val_data = create_data_dicts(val_df)
        test_data = create_data_dicts(test_df)
        
        return train_data, val_data, test_data
    
    def train_improved_model(self, num_epochs=10, batch_size=8, learning_rate=1e-4):
        """Train improved MONAI model with robust validation"""
        
        print("🚀 STARTING IMPROVED MONAI TRAINING")
        print("=" * 50)
        
        # Prepare data
        train_data, val_data, test_data = self.prepare_data()
        
        # Create transforms
        train_transform, val_transform = self.create_robust_transforms()
        
        # Create robust datasets
        train_dataset = self.create_robust_dataset(train_data, train_transform, is_training=True)
        val_dataset = self.create_robust_dataset(val_data, val_transform, is_training=False)
        
        # Create data loaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True, num_workers=0, pin_memory=True
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False, num_workers=0, pin_memory=True
        )
        
        # Create MONAI model
        model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=self.num_classes,
            pretrained=True
        ).to(self.device)
        
        # Training setup
        criterion = nn.CrossEntropyLoss(label_smoothing=0.1)  # Label smoothing for robustness
        optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
        
        # Training history
        history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
        best_val_acc = 0.0
        best_model_state = None
        
        print(f"🏥 Training for {num_epochs} epochs with robust validation...")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 30)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, batch_data in enumerate(train_loader):
                try:
                    inputs = batch_data["image"].to(self.device)
                    labels = batch_data["label"].to(self.device)
                    
                    # Skip batch if inputs contain NaN or inf
                    if torch.isnan(inputs).any() or torch.isinf(inputs).any():
                        print(f"⚠️ Skipping batch {batch_idx} due to invalid inputs")
                        continue
                    
                    optimizer.zero_grad()
                    outputs = model(inputs)
                    loss = criterion(outputs, labels)
                    
                    # Check for NaN loss
                    if torch.isnan(loss):
                        print(f"⚠️ NaN loss detected, skipping batch {batch_idx}")
                        continue
                    
                    loss.backward()
                    
                    # Gradient clipping for stability
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    train_total += labels.size(0)
                    train_correct += (predicted == labels).sum().item()
                    
                    if batch_idx % 20 == 0:
                        current_acc = 100. * train_correct / train_total if train_total > 0 else 0
                        print(f"  Batch {batch_idx:3d} | Loss: {loss.item():.4f} | Acc: {current_acc:.1f}%")
                
                except Exception as e:
                    print(f"⚠️ Error in training batch {batch_idx}: {e}")
                    continue
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for batch_data in val_loader:
                    try:
                        inputs = batch_data["image"].to(self.device)
                        labels = batch_data["label"].to(self.device)
                        
                        # Skip batch if inputs contain NaN or inf
                        if torch.isnan(inputs).any() or torch.isinf(inputs).any():
                            continue
                        
                        outputs = model(inputs)
                        loss = criterion(outputs, labels)
                        
                        if torch.isnan(loss):
                            continue
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += labels.size(0)
                        val_correct += (predicted == labels).sum().item()
                        
                        all_predictions.extend(predicted.cpu().numpy())
                        all_labels.extend(labels.cpu().numpy())
                    
                    except Exception as e:
                        print(f"⚠️ Error in validation batch: {e}")
                        continue
            
            # Calculate metrics
            if train_total > 0 and val_total > 0:
                train_loss_avg = train_loss / len(train_loader)
                train_acc = 100. * train_correct / train_total
                val_loss_avg = val_loss / len(val_loader)
                val_acc = 100. * val_correct / val_total
                
                # Update history
                history['train_loss'].append(train_loss_avg)
                history['train_acc'].append(train_acc)
                history['val_loss'].append(val_loss_avg)
                history['val_acc'].append(val_acc)
                
                # Update learning rate
                scheduler.step()
                
                # Print epoch summary
                print(f"\nEpoch {epoch+1} Summary:")
                print(f"  Train: Loss {train_loss_avg:.4f}, Acc {train_acc:.1f}%")
                print(f"  Val:   Loss {val_loss_avg:.4f}, Acc {val_acc:.1f}%")
                
                # Per-class accuracy
                if len(all_predictions) > 0 and len(all_labels) > 0:
                    print(f"  Per-class validation accuracy:")
                    for i, class_name in enumerate(self.class_names):
                        class_mask = np.array(all_labels) == i
                        if class_mask.sum() > 0:
                            class_acc = (np.array(all_predictions)[class_mask] == np.array(all_labels)[class_mask]).mean() * 100
                            class_count = class_mask.sum()
                            print(f"    {class_name:15}: {class_acc:5.1f}% ({class_count} samples)")
                
                # Save best model
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    best_model_state = model.state_dict().copy()
                    
                    # Save checkpoint
                    checkpoint = {
                        'epoch': epoch + 1,
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scheduler_state_dict': scheduler.state_dict(),
                        'best_val_acc': best_val_acc,
                        'history': history,
                        'monai_version': monai.__version__
                    }
                    
                    Path("models").mkdir(exist_ok=True)
                    torch.save(checkpoint, "models/improved_monai_densenet121.pth")
                    print(f"  💾 New best model saved! Val Acc: {val_acc:.1f}%")
                
                # Early stopping for excellent performance
                if val_acc > 96.0:
                    print(f"🎉 Excellent performance achieved!")
                    break
            else:
                print("⚠️ No valid batches processed in this epoch")
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        print(f"\n✅ Improved MONAI training completed!")
        print(f"🏆 Best validation accuracy: {best_val_acc:.1f}%")
        
        return model, history, best_val_acc

def main():
    """Main training function"""
    
    try:
        trainer = ImprovedMONAITrainer()
        model, history, best_acc = trainer.train_improved_model(
            num_epochs=8,
            batch_size=6,
            learning_rate=1e-4
        )
        
        print(f"\n🎉 IMPROVED MONAI TRAINING RESULTS:")
        print(f"   🏥 Robust medical model trained")
        print(f"   📊 Best accuracy: {best_acc:.1f}%")
        print(f"   💾 Model saved as 'improved_monai_densenet121.pth'")
        print(f"   🔧 Enhanced error handling and validation")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check dataset manifest exists")
        print("2. Verify image files are accessible")
        print("3. Ensure sufficient disk space")
        print("4. Check MONAI installation")

if __name__ == "__main__":
    main()
