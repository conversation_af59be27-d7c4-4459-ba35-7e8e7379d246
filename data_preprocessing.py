"""
Data Preprocessing Pipeline for Foot Deformity Classification
Handles data loading, augmentation, and preparation for training
"""

import os
import pandas as pd
import numpy as np
import cv2
from PIL import Image
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class FootDeformityDataset(Dataset):
    """Custom Dataset class for foot deformity images"""
    
    def __init__(self, dataframe, root_dir, transform=None, target_transform=None):
        self.dataframe = dataframe.reset_index(drop=True)
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.target_transform = target_transform
        
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
        
        # Get image path and label
        row = self.dataframe.iloc[idx]
        img_path = self.root_dir / row['class'] / row['filename']
        label = row['class_id']
        
        # Load image
        try:
            image = Image.open(img_path).convert('RGB')
        except Exception as e:
            print(f"Error loading image {img_path}: {e}")
            # Return a black image as fallback
            image = Image.new('RGB', (224, 224), (0, 0, 0))
        
        # Apply transforms
        if self.transform:
            image = self.transform(image)
        
        if self.target_transform:
            label = self.target_transform(label)
            
        return image, label

class DataPreprocessor:
    """Data preprocessing and augmentation pipeline"""
    
    def __init__(self, dataset_path="processed_dataset", img_size=224):
        self.dataset_path = Path(dataset_path)
        self.img_size = img_size
        self.manifest_df = None
        self.class_weights = None
        
    def load_manifest(self):
        """Load the dataset manifest"""
        manifest_path = self.dataset_path / 'dataset_manifest.csv'
        if not manifest_path.exists():
            raise FileNotFoundError(f"Dataset manifest not found at {manifest_path}")
        
        self.manifest_df = pd.read_csv(manifest_path)
        print(f"📄 Loaded manifest with {len(self.manifest_df)} images")
        return self.manifest_df
    
    def analyze_class_distribution(self):
        """Analyze and visualize class distribution"""
        if self.manifest_df is None:
            self.load_manifest()
        
        print("\n📊 Class Distribution Analysis")
        print("=" * 40)
        
        class_counts = self.manifest_df['class'].value_counts()
        class_percentages = self.manifest_df['class'].value_counts(normalize=True) * 100
        
        for class_name in class_counts.index:
            count = class_counts[class_name]
            percentage = class_percentages[class_name]
            print(f"{class_name:15}: {count:4d} images ({percentage:5.1f}%)")
        
        # Calculate class weights for balanced training
        total_samples = len(self.manifest_df)
        n_classes = len(class_counts)
        
        self.class_weights = {}
        for class_name, count in class_counts.items():
            weight = total_samples / (n_classes * count)
            self.class_weights[class_name] = weight
            
        print(f"\n⚖️  Class Weights (for balanced training):")
        for class_name, weight in self.class_weights.items():
            print(f"{class_name:15}: {weight:.3f}")
        
        return class_counts, self.class_weights
    
    def create_data_splits(self, test_size=0.2, val_size=0.1, random_state=42):
        """Create train/validation/test splits"""
        if self.manifest_df is None:
            self.load_manifest()
        
        print(f"\n🔄 Creating Data Splits...")
        
        # Stratified split to maintain class distribution
        X = self.manifest_df.drop(['class_id'], axis=1)
        y = self.manifest_df['class_id']
        
        # First split: train+val vs test
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # Second split: train vs val
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, 
            random_state=random_state, stratify=y_temp
        )
        
        # Combine features and labels
        train_df = pd.concat([X_train, y_train], axis=1)
        val_df = pd.concat([X_val, y_val], axis=1)
        test_df = pd.concat([X_test, y_test], axis=1)
        
        print(f"📊 Data Split Summary:")
        print(f"  Training:   {len(train_df):4d} images ({len(train_df)/len(self.manifest_df)*100:.1f}%)")
        print(f"  Validation: {len(val_df):4d} images ({len(val_df)/len(self.manifest_df)*100:.1f}%)")
        print(f"  Testing:    {len(test_df):4d} images ({len(test_df)/len(self.manifest_df)*100:.1f}%)")
        
        return train_df, val_df, test_df
    
    def get_transforms(self):
        """Define data augmentation and preprocessing transforms"""
        
        # Training transforms with augmentation
        train_transforms = transforms.Compose([
            transforms.Resize((self.img_size + 32, self.img_size + 32)),
            transforms.RandomCrop(self.img_size),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Validation/Test transforms (no augmentation)
        val_transforms = transforms.Compose([
            transforms.Resize((self.img_size, self.img_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return train_transforms, val_transforms
    
    def create_data_loaders(self, train_df, val_df, test_df, batch_size=32, num_workers=4):
        """Create PyTorch data loaders"""
        
        train_transforms, val_transforms = self.get_transforms()
        
        # Create datasets
        train_dataset = FootDeformityDataset(
            train_df, self.dataset_path, transform=train_transforms
        )
        
        val_dataset = FootDeformityDataset(
            val_df, self.dataset_path, transform=val_transforms
        )
        
        test_dataset = FootDeformityDataset(
            test_df, self.dataset_path, transform=val_transforms
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True, 
            num_workers=num_workers, pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        
        print(f"\n🔄 Data Loaders Created:")
        print(f"  Train: {len(train_loader)} batches ({len(train_dataset)} images)")
        print(f"  Val:   {len(val_loader)} batches ({len(val_dataset)} images)")
        print(f"  Test:  {len(test_loader)} batches ({len(test_dataset)} images)")
        
        return train_loader, val_loader, test_loader
    
    def visualize_samples(self, dataloader, num_samples=8):
        """Visualize sample images from the dataset"""
        
        # Get class names
        class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        
        # Get a batch of images
        data_iter = iter(dataloader)
        images, labels = next(data_iter)
        
        # Denormalize images for visualization
        mean = torch.tensor([0.485, 0.456, 0.406])
        std = torch.tensor([0.229, 0.224, 0.225])
        
        fig, axes = plt.subplots(2, 4, figsize=(15, 8))
        axes = axes.ravel()
        
        for i in range(min(num_samples, len(images))):
            img = images[i]
            label = labels[i].item()
            
            # Denormalize
            img = img * std[:, None, None] + mean[:, None, None]
            img = torch.clamp(img, 0, 1)
            
            # Convert to numpy and transpose
            img_np = img.permute(1, 2, 0).numpy()
            
            axes[i].imshow(img_np)
            axes[i].set_title(f'Class: {class_names[label]}')
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('sample_images.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("📸 Sample images saved as 'sample_images.png'")

def main():
    """Main preprocessing pipeline"""
    print("🚀 Starting Data Preprocessing Pipeline")
    print("=" * 50)
    
    # Initialize preprocessor
    preprocessor = DataPreprocessor()
    
    # Load and analyze data
    preprocessor.load_manifest()
    preprocessor.analyze_class_distribution()
    
    # Create data splits
    train_df, val_df, test_df = preprocessor.create_data_splits()
    
    # Create data loaders
    train_loader, val_loader, test_loader = preprocessor.create_data_loaders(
        train_df, val_df, test_df, batch_size=32
    )
    
    # Visualize samples
    print("\n📸 Visualizing sample images...")
    preprocessor.visualize_samples(train_loader)
    
    print("\n✅ Data preprocessing completed successfully!")
    print("🎯 Ready for model training!")
    
    return train_loader, val_loader, test_loader, preprocessor.class_weights

if __name__ == "__main__":
    train_loader, val_loader, test_loader, class_weights = main()
