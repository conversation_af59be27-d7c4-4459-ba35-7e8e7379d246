"""
Improve Hallux Valgus Detection for External Validation
Analysis and improvement of detection accuracy for external dataset
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
from PIL import Image
import torchvision.transforms as transforms
import json
import matplotlib.pyplot as plt
import seaborn as sns

# MONAI imports
import monai
from monai.networks.nets import DenseNet121

class HalluxValgusDetectionImprover:
    """Improve Hallux Valgus detection accuracy"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.models = {}
        
        print("🔧 HALLUX VALGUS DETECTION IMPROVEMENT")
        print("=" * 60)
    
    def load_all_models(self):
        """Load all available models for comparison"""
        
        model_paths = {
            'monai': 'models/monai_densenet121_simple.pth',
            'standard': 'models/best_resnet50_model.pth',
            'improved_monai': 'models/improved_monai_densenet121.pth'
        }
        
        for model_name, model_path in model_paths.items():
            if Path(model_path).exists():
                try:
                    if model_name == 'monai' or model_name == 'improved_monai':
                        model = self.load_monai_model(model_path)
                    else:
                        model = self.load_standard_model(model_path)
                    
                    if model is not None:
                        self.models[model_name] = model
                        print(f"✅ Loaded {model_name} model")
                    
                except Exception as e:
                    print(f"❌ Failed to load {model_name}: {e}")
            else:
                print(f"⚠️ Model not found: {model_path}")
    
    def load_monai_model(self, model_path):
        """Load MONAI model"""
        
        model = DenseNet121(
            spatial_dims=2,
            in_channels=3,
            out_channels=4,
            pretrained=False
        )
        
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()
        
        return model
    
    def load_standard_model(self, model_path):
        """Load standard ResNet50 model"""
        
        import torchvision.models as models
        
        model = models.resnet50(pretrained=False)
        model.fc = torch.nn.Linear(model.fc.in_features, 4)
        
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()
        
        return model
    
    def create_enhanced_transforms(self):
        """Create enhanced transforms for better Hallux Valgus detection"""
        
        # Standard transform
        standard_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Enhanced transform with better preprocessing
        enhanced_transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.CenterCrop((224, 224)),
            transforms.ColorJitter(brightness=0.1, contrast=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # High contrast transform for better feature detection
        contrast_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ColorJitter(contrast=0.3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return {
            'standard': standard_transform,
            'enhanced': enhanced_transform,
            'contrast': contrast_transform
        }
    
    def ensemble_prediction(self, image_path):
        """Make ensemble prediction using multiple models and transforms"""
        
        if not self.models:
            print("❌ No models loaded")
            return None
        
        # Load image
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception as e:
            print(f"❌ Failed to load {image_path}: {e}")
            return None
        
        transforms_dict = self.create_enhanced_transforms()
        all_predictions = []
        
        # Test with each model and transform combination
        for model_name, model in self.models.items():
            for transform_name, transform in transforms_dict.items():
                try:
                    # Apply transform
                    image_tensor = transform(image).unsqueeze(0).to(self.device)
                    
                    # Make prediction
                    with torch.no_grad():
                        outputs = model(image_tensor)
                        probabilities = F.softmax(outputs, dim=1)
                        
                        all_predictions.append({
                            'model': model_name,
                            'transform': transform_name,
                            'probabilities': probabilities[0].cpu().numpy()
                        })
                
                except Exception as e:
                    print(f"⚠️ Prediction failed for {model_name} + {transform_name}: {e}")
        
        if not all_predictions:
            return None
        
        # Ensemble the predictions
        ensemble_probs = np.mean([p['probabilities'] for p in all_predictions], axis=0)
        predicted_class_id = np.argmax(ensemble_probs)
        confidence = ensemble_probs[predicted_class_id]
        
        return {
            'image_path': str(image_path),
            'predicted_class_id': predicted_class_id,
            'predicted_class': self.class_names[predicted_class_id],
            'confidence': confidence,
            'ensemble_probabilities': ensemble_probs.tolist(),
            'individual_predictions': all_predictions
        }
    
    def analyze_external_dataset(self, dataset_path):
        """Analyze external dataset with improved methods"""
        
        dataset_path = Path(dataset_path)
        print(f"\n🔍 IMPROVED EXTERNAL VALIDATION")
        print(f"Dataset: {dataset_path}")
        print("-" * 50)
        
        # Find images
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        all_images = []
        
        for ext in image_extensions:
            all_images.extend(dataset_path.rglob(f"*{ext}"))
            all_images.extend(dataset_path.rglob(f"*{ext.upper()}"))
        
        if not all_images:
            print(f"❌ No images found")
            return None
        
        print(f"📊 Found {len(all_images)} images")
        
        # Process with ensemble
        results = []
        for i, image_path in enumerate(all_images):
            if i % 10 == 0:
                print(f"Progress: {i+1}/{len(all_images)}")
            
            prediction = self.ensemble_prediction(image_path)
            if prediction:
                results.append(prediction)
        
        return self.analyze_improved_results(results)
    
    def analyze_improved_results(self, results):
        """Analyze improved results"""
        
        print(f"\n📊 IMPROVED ANALYSIS RESULTS")
        print("=" * 50)
        
        total = len(results)
        
        # Class distribution
        class_counts = {}
        for class_name in self.class_names:
            count = sum(1 for r in results if r['predicted_class'] == class_name)
            class_counts[class_name] = count
            print(f"{class_name:15}: {count:3d} ({count/total*100:5.1f}%)")
        
        # Confidence analysis
        confidences = [r['confidence'] for r in results]
        avg_confidence = np.mean(confidences)
        
        print(f"\nConfidence Analysis:")
        print(f"Average confidence: {avg_confidence:.3f}")
        print(f"High confidence (>0.8): {sum(1 for c in confidences if c > 0.8)}")
        print(f"Medium confidence (0.5-0.8): {sum(1 for c in confidences if 0.5 <= c <= 0.8)}")
        print(f"Low confidence (<0.5): {sum(1 for c in confidences if c < 0.5)}")
        
        # Hallux Valgus specific analysis
        hallux_predictions = [r for r in results if r['predicted_class'] == 'hallux_valgus']
        hallux_high_conf = [r for r in hallux_predictions if r['confidence'] > 0.8]
        
        print(f"\n🦶 HALLUX VALGUS ANALYSIS:")
        print(f"Total Hallux Valgus predictions: {len(hallux_predictions)}")
        print(f"High confidence Hallux Valgus: {len(hallux_high_conf)}")
        print(f"Detection rate: {len(hallux_predictions)/total*100:.1f}%")
        print(f"High confidence rate: {len(hallux_high_conf)/total*100:.1f}%")
        
        # Recommendations
        self.provide_recommendations(results)
        
        return results
    
    def provide_recommendations(self, results):
        """Provide recommendations for improving detection"""
        
        print(f"\n💡 RECOMMENDATIONS FOR IMPROVEMENT:")
        print("-" * 50)
        
        total = len(results)
        hallux_count = sum(1 for r in results if r['predicted_class'] == 'hallux_valgus')
        hallux_rate = hallux_count / total * 100
        
        if hallux_rate < 50:
            print("🔧 LOW DETECTION RATE RECOMMENDATIONS:")
            print("   1. Check image quality - ensure clear foot visibility")
            print("   2. Verify image orientation - foot should be clearly visible")
            print("   3. Consider image preprocessing - enhance contrast/brightness")
            print("   4. Check for proper foot positioning in images")
            
        elif hallux_rate < 70:
            print("⚠️ MODERATE DETECTION RATE RECOMMENDATIONS:")
            print("   1. Fine-tune model with similar external images")
            print("   2. Apply data augmentation during training")
            print("   3. Consider ensemble methods (already implemented)")
            print("   4. Validate image quality and consistency")
            
        else:
            print("✅ GOOD DETECTION RATE:")
            print("   1. Current performance is acceptable")
            print("   2. Consider confidence threshold adjustment")
            print("   3. Monitor for edge cases")
            
        # Confidence recommendations
        avg_conf = np.mean([r['confidence'] for r in results])
        if avg_conf < 0.7:
            print("\n🎯 CONFIDENCE IMPROVEMENT:")
            print("   1. Retrain model with more diverse data")
            print("   2. Apply transfer learning from medical datasets")
            print("   3. Use uncertainty quantification methods")
        
        print(f"\n📈 NEXT STEPS:")
        print(f"   1. Collect more Hallux Valgus training data")
        print(f"   2. Apply domain adaptation techniques")
        print(f"   3. Fine-tune model on external dataset samples")
        print(f"   4. Implement active learning for difficult cases")

def main():
    """Main improvement function"""
    
    external_dataset_path = r"C:\Users\<USER>\Desktop\External Vaildation Dataset"
    
    try:
        improver = HalluxValgusDetectionImprover()
        improver.load_all_models()
        
        if not improver.models:
            print("❌ No models available for improvement")
            return
        
        print(f"\n🚀 Starting improved analysis...")
        results = improver.analyze_external_dataset(external_dataset_path)
        
        if results:
            print(f"\n🎉 IMPROVED ANALYSIS COMPLETED!")
            print(f"   Check recommendations above for next steps")
        
    except Exception as e:
        print(f"❌ Improvement analysis failed: {e}")

if __name__ == "__main__":
    main()
