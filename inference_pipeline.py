"""
Inference Pipeline for Foot Deformity Classification
Provides easy-to-use interface for making predictions on new images
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
import cv2
from pathlib import Path
import json
import time

from model_architectures import create_model

class FootDeformityPredictor:
    """Easy-to-use predictor for foot deformity classification"""
    
    def __init__(self, model_path, model_type='resnet50', device=None):
        self.model_type = model_type
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        self.class_descriptions = {
            'normal': 'Normal foot - no deformity detected',
            'flatfoot': 'Flatfoot (Pes Planus) - collapsed arch',
            'foot_ulcer': 'Foot ulcer - skin lesion detected',
            'hallux_valgus': 'Hallux Valgus - bunion deformity'
        }
        
        # Load model
        self.model = self._load_model(model_path)
        
        # Setup transforms
        self.transform = self._setup_transforms()
        
        print(f"🚀 Predictor initialized with {model_type} on {self.device}")
    
    def _load_model(self, model_path):
        """Load the trained model"""
        
        print(f"📥 Loading model from {model_path}")
        
        # Create model architecture
        model = create_model(
            model_type=self.model_type,
            num_classes=len(self.class_names),
            pretrained=False  # We're loading trained weights
        )
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # Load model state
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ Model loaded from checkpoint (epoch {checkpoint.get('epoch', 'unknown')})")
        else:
            model.load_state_dict(checkpoint)
            print("✅ Model loaded from state dict")
        
        model.to(self.device)
        model.eval()
        
        return model
    
    def _setup_transforms(self):
        """Setup image preprocessing transforms"""
        
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return transform
    
    def preprocess_image(self, image_input):
        """Preprocess image for prediction"""
        
        # Handle different input types
        if isinstance(image_input, str) or isinstance(image_input, Path):
            # Load from file path
            image = Image.open(image_input).convert('RGB')
        elif isinstance(image_input, np.ndarray):
            # Convert numpy array to PIL Image
            if image_input.dtype == np.uint8:
                image = Image.fromarray(image_input)
            else:
                image = Image.fromarray((image_input * 255).astype(np.uint8))
        elif isinstance(image_input, Image.Image):
            # Already a PIL Image
            image = image_input.convert('RGB')
        else:
            raise ValueError(f"Unsupported image input type: {type(image_input)}")
        
        # Apply transforms
        image_tensor = self.transform(image).unsqueeze(0)  # Add batch dimension
        
        return image_tensor, image
    
    def predict_single(self, image_input, return_confidence=True):
        """Make prediction on a single image"""
        
        # Preprocess image
        image_tensor, original_image = self.preprocess_image(image_input)
        image_tensor = image_tensor.to(self.device)
        
        # Make prediction
        with torch.no_grad():
            start_time = time.time()
            outputs = self.model(image_tensor)
            inference_time = time.time() - start_time
            
            # Get probabilities
            probabilities = F.softmax(outputs, dim=1)
            confidence_scores = probabilities.cpu().numpy()[0]
            
            # Get prediction
            predicted_class_id = torch.argmax(outputs, dim=1).item()
            predicted_class = self.class_names[predicted_class_id]
            confidence = confidence_scores[predicted_class_id]
        
        result = {
            'predicted_class': predicted_class,
            'predicted_class_id': predicted_class_id,
            'confidence': float(confidence),
            'description': self.class_descriptions[predicted_class],
            'inference_time': inference_time,
            'all_probabilities': {
                self.class_names[i]: float(confidence_scores[i]) 
                for i in range(len(self.class_names))
            }
        }
        
        if return_confidence:
            return result
        else:
            return predicted_class
    
    def predict_batch(self, image_paths, batch_size=32):
        """Make predictions on a batch of images"""
        
        print(f"🔄 Processing {len(image_paths)} images in batches of {batch_size}")
        
        results = []
        
        for i in range(0, len(image_paths), batch_size):
            batch_paths = image_paths[i:i+batch_size]
            batch_tensors = []
            
            # Preprocess batch
            for path in batch_paths:
                try:
                    tensor, _ = self.preprocess_image(path)
                    batch_tensors.append(tensor)
                except Exception as e:
                    print(f"⚠️  Error processing {path}: {e}")
                    # Add dummy tensor for failed images
                    batch_tensors.append(torch.zeros(1, 3, 224, 224))
            
            # Stack tensors
            batch_tensor = torch.cat(batch_tensors, dim=0).to(self.device)
            
            # Make predictions
            with torch.no_grad():
                outputs = self.model(batch_tensor)
                probabilities = F.softmax(outputs, dim=1)
                predictions = torch.argmax(outputs, dim=1)
            
            # Process results
            for j, path in enumerate(batch_paths):
                pred_id = predictions[j].item()
                pred_class = self.class_names[pred_id]
                confidence = probabilities[j, pred_id].item()
                
                result = {
                    'image_path': str(path),
                    'predicted_class': pred_class,
                    'confidence': confidence,
                    'description': self.class_descriptions[pred_class]
                }
                results.append(result)
            
            print(f"  Processed batch {i//batch_size + 1}/{(len(image_paths)-1)//batch_size + 1}")
        
        return results
    
    def visualize_prediction(self, image_input, save_path=None):
        """Visualize prediction with confidence scores"""
        
        # Make prediction
        result = self.predict_single(image_input)
        
        # Preprocess image for visualization
        _, original_image = self.preprocess_image(image_input)
        
        # Create visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Display original image
        ax1.imshow(original_image)
        ax1.set_title(f'Input Image', fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # Display prediction results
        classes = list(result['all_probabilities'].keys())
        probabilities = list(result['all_probabilities'].values())
        colors = ['green' if cls == result['predicted_class'] else 'lightblue' for cls in classes]
        
        bars = ax2.barh(classes, probabilities, color=colors)
        ax2.set_xlabel('Confidence Score', fontsize=12)
        ax2.set_title('Prediction Confidence', fontsize=14, fontweight='bold')
        ax2.set_xlim(0, 1)
        
        # Add confidence values on bars
        for i, (bar, prob) in enumerate(zip(bars, probabilities)):
            ax2.text(prob + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.3f}', va='center', fontweight='bold')
        
        # Add prediction summary
        pred_text = f"Prediction: {result['predicted_class'].upper()}\n"
        pred_text += f"Confidence: {result['confidence']:.1%}\n"
        pred_text += f"Description: {result['description']}\n"
        pred_text += f"Inference Time: {result['inference_time']*1000:.1f}ms"
        
        plt.figtext(0.02, 0.02, pred_text, fontsize=10, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Prediction visualization saved as '{save_path}'")
        
        plt.show()
        
        return result
    
    def analyze_image_directory(self, directory_path, save_results=True):
        """Analyze all images in a directory"""
        
        directory = Path(directory_path)
        
        # Find all image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_paths = []
        
        for ext in image_extensions:
            image_paths.extend(list(directory.glob(f'*{ext}')))
            image_paths.extend(list(directory.glob(f'*{ext.upper()}')))
        
        print(f"📁 Found {len(image_paths)} images in {directory}")
        
        if len(image_paths) == 0:
            print("⚠️  No images found!")
            return []
        
        # Make predictions
        results = self.predict_batch(image_paths)
        
        # Analyze results
        class_counts = {}
        for result in results:
            pred_class = result['predicted_class']
            class_counts[pred_class] = class_counts.get(pred_class, 0) + 1
        
        print(f"\n📊 Analysis Results:")
        for class_name, count in class_counts.items():
            percentage = (count / len(results)) * 100
            print(f"  {class_name}: {count} images ({percentage:.1f}%)")
        
        # Save results
        if save_results:
            results_path = directory / 'prediction_results.json'
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\n💾 Results saved to {results_path}")
        
        return results

def main():
    """Example usage of the inference pipeline"""
    
    print("🔮 Foot Deformity Inference Pipeline")
    print("=" * 40)
    
    # Example usage (requires trained model)
    # predictor = FootDeformityPredictor('models/best_resnet50_model.pth')
    
    # Single image prediction
    # result = predictor.predict_single('path/to/image.jpg')
    # print(f"Prediction: {result['predicted_class']} ({result['confidence']:.1%})")
    
    # Visualize prediction
    # predictor.visualize_prediction('path/to/image.jpg', 'prediction_result.png')
    
    # Batch prediction
    # results = predictor.analyze_image_directory('path/to/image/directory')
    
    print("🚀 Inference pipeline ready for use!")
    print("📋 Make sure to train a model first using training_pipeline.py")

if __name__ == "__main__":
    main()
