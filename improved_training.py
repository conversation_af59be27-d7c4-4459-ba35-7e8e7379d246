"""
Improved Training Pipeline for Better Model Performance
Addresses class imbalance and bias issues
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.utils.class_weight import compute_class_weight
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# Import our modules
from data_preprocessing import DataPreprocessor, FootDeformityDataset
from model_architectures import create_model

class ImprovedTrainer:
    """Improved trainer with better class balancing"""
    
    def __init__(self, model_type='resnet50', num_classes=4):
        self.model_type = model_type
        self.num_classes = num_classes
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['normal', 'flatfoot', 'foot_ulcer', 'hallux_valgus']
        
        print(f"🚀 Improved Trainer initialized on {self.device}")
    
    def create_balanced_sampler(self, train_df):
        """Create weighted sampler for balanced training"""
        
        # Calculate class weights
        class_counts = train_df['class_id'].value_counts().sort_index()
        total_samples = len(train_df)
        
        # Compute balanced class weights
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(train_df['class_id']),
            y=train_df['class_id']
        )
        
        print(f"📊 Class distribution in training:")
        for i, (class_name, weight) in enumerate(zip(self.class_names, class_weights)):
            count = class_counts[i] if i in class_counts else 0
            print(f"   {class_name:15}: {count:4d} samples, weight: {weight:.3f}")
        
        # Create sample weights
        sample_weights = [class_weights[class_id] for class_id in train_df['class_id']]
        
        # Create weighted sampler
        sampler = WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
        
        return sampler, class_weights
    
    def create_improved_data_loaders(self, train_df, val_df, test_df, batch_size=16):
        """Create data loaders with balanced sampling"""
        
        preprocessor = DataPreprocessor()
        train_transforms, val_transforms = preprocessor.get_transforms()
        
        # Create datasets
        train_dataset = FootDeformityDataset(
            train_df, "processed_dataset", transform=train_transforms
        )
        
        val_dataset = FootDeformityDataset(
            val_df, "processed_dataset", transform=val_transforms
        )
        
        test_dataset = FootDeformityDataset(
            test_df, "processed_dataset", transform=val_transforms
        )
        
        # Create balanced sampler for training
        sampler, class_weights = self.create_balanced_sampler(train_df)
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset, 
            batch_size=batch_size, 
            sampler=sampler,  # Use weighted sampler instead of shuffle
            num_workers=0, 
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset, 
            batch_size=batch_size, 
            shuffle=False,
            num_workers=0, 
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset, 
            batch_size=batch_size, 
            shuffle=False,
            num_workers=0, 
            pin_memory=True
        )
        
        return train_loader, val_loader, test_loader, class_weights
    
    def train_improved_model(self, num_epochs=15, batch_size=16, learning_rate=0.0001):
        """Train model with improved techniques"""
        
        print(f"\n🎯 Starting Improved Training")
        print("=" * 50)
        
        # Load data
        preprocessor = DataPreprocessor()
        preprocessor.load_manifest()
        
        # Create balanced splits
        train_df, val_df, test_df = preprocessor.create_data_splits()
        
        # Create improved data loaders
        train_loader, val_loader, test_loader, class_weights = self.create_improved_data_loaders(
            train_df, val_df, test_df, batch_size
        )
        
        # Create model
        model = create_model(self.model_type, num_classes=self.num_classes, pretrained=True)
        model.to(self.device)
        
        # Freeze backbone initially for stable training
        if hasattr(model, 'backbone'):
            for param in model.backbone.parameters():
                param.requires_grad = False
            print("🔒 Backbone frozen for initial training")
        
        # Setup training with class weights
        class_weight_tensor = torch.FloatTensor(class_weights).to(self.device)
        criterion = nn.CrossEntropyLoss(weight=class_weight_tensor)
        
        # Use smaller learning rate for better convergence
        optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=3, verbose=True
        )
        
        # Training history
        history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': []
        }
        
        best_val_acc = 0.0
        best_model_state = None
        
        print(f"🚀 Training for {num_epochs} epochs...")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 30)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (images, labels) in enumerate(train_loader):
                images, labels = images.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                if batch_idx % 50 == 0:
                    current_acc = 100. * train_correct / train_total
                    print(f"  Batch {batch_idx:3d} | Loss: {loss.item():.4f} | Acc: {current_acc:.1f}%")
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for images, labels in val_loader:
                    images, labels = images.to(self.device), labels.to(self.device)
                    
                    outputs = model(images)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels.size(0)
                    val_correct += (predicted == labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            # Calculate metrics
            train_loss_avg = train_loss / len(train_loader)
            train_acc = 100. * train_correct / train_total
            val_loss_avg = val_loss / len(val_loader)
            val_acc = 100. * val_correct / val_total
            
            # Update history
            history['train_loss'].append(train_loss_avg)
            history['train_acc'].append(train_acc)
            history['val_loss'].append(val_loss_avg)
            history['val_acc'].append(val_acc)
            
            # Print epoch summary
            print(f"\nEpoch {epoch+1} Summary:")
            print(f"  Train: Loss {train_loss_avg:.4f}, Acc {train_acc:.1f}%")
            print(f"  Val:   Loss {val_loss_avg:.4f}, Acc {val_acc:.1f}%")
            
            # Per-class validation accuracy
            print(f"  Per-class validation accuracy:")
            for i, class_name in enumerate(self.class_names):
                class_mask = np.array(all_labels) == i
                if class_mask.sum() > 0:
                    class_acc = (np.array(all_predictions)[class_mask] == np.array(all_labels)[class_mask]).mean() * 100
                    class_count = class_mask.sum()
                    print(f"    {class_name:15}: {class_acc:5.1f}% ({class_count} samples)")
            
            # Update learning rate
            scheduler.step(val_acc)
            
            # Save best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_model_state = model.state_dict().copy()
                
                # Save checkpoint
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_val_acc': best_val_acc,
                    'class_weights': class_weights,
                    'history': history
                }
                
                Path("models").mkdir(exist_ok=True)
                torch.save(checkpoint, "models/improved_resnet50_model.pth")
                print(f"  💾 New best model saved! Val Acc: {val_acc:.1f}%")
            
            # Unfreeze backbone after a few epochs for fine-tuning
            if epoch == 3 and hasattr(model, 'backbone'):
                for param in model.backbone.parameters():
                    param.requires_grad = True
                # Reduce learning rate for fine-tuning
                for param_group in optimizer.param_groups:
                    param_group['lr'] = learning_rate * 0.1
                print("🔓 Backbone unfrozen for fine-tuning")
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        print(f"\n✅ Training completed!")
        print(f"🏆 Best validation accuracy: {best_val_acc:.1f}%")
        
        return model, history, test_loader
    
    def evaluate_improved_model(self, model, test_loader):
        """Comprehensive evaluation of the improved model"""
        
        print(f"\n📊 Evaluating Improved Model")
        print("=" * 50)
        
        model.eval()
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                
                outputs = model(images)
                probabilities = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # Calculate overall accuracy
        accuracy = (np.array(all_predictions) == np.array(all_labels)).mean() * 100
        print(f"🎯 Overall Test Accuracy: {accuracy:.1f}%")
        
        # Classification report
        print(f"\n📋 Detailed Classification Report:")
        report = classification_report(
            all_labels, all_predictions, 
            target_names=self.class_names, 
            digits=3
        )
        print(report)
        
        # Confusion matrix
        cm = confusion_matrix(all_labels, all_predictions)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('Improved Model - Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.tight_layout()
        plt.savefig('improved_model_confusion_matrix.png', dpi=150)
        plt.show()
        
        print(f"📊 Confusion matrix saved as 'improved_model_confusion_matrix.png'")
        
        return accuracy, all_predictions, all_labels

def main():
    """Run improved training"""
    
    print("🔧 IMPROVED MODEL TRAINING")
    print("=" * 60)
    
    trainer = ImprovedTrainer()
    
    # Train improved model
    model, history, test_loader = trainer.train_improved_model(
        num_epochs=10,  # Fewer epochs for testing
        batch_size=16,
        learning_rate=0.0001  # Smaller learning rate
    )
    
    # Evaluate improved model
    accuracy, predictions, labels = trainer.evaluate_improved_model(model, test_loader)
    
    print(f"\n🎉 IMPROVED MODEL RESULTS:")
    print(f"   🎯 Test Accuracy: {accuracy:.1f}%")
    print(f"   📈 Model saved as 'improved_resnet50_model.pth'")
    print(f"   📊 Confusion matrix generated")
    
    if accuracy > 75:
        print(f"   ✅ Model performance is good!")
    else:
        print(f"   ⚠️  Model needs further improvement")

if __name__ == "__main__":
    main()
